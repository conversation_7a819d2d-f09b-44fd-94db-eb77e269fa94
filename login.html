<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
		<title>四方支付系统 - 安全登录</title>
		<meta name="description" content="专业的四方支付系统管理平台">
		<meta name="keywords" content="四方支付,支付系统,管理平台">
		<!-- 样 式 文 件 -->
		<link rel="stylesheet" href="component/pear/css/pear.css" />
		<link rel="stylesheet" href="admin/css/other/login.css" />
		<!-- 图标 -->
		<link rel="icon" href="admin/images/favicon.ico" type="image/x-icon">
	</head>
    <!-- 代 码 结 构 -->
	<body>
		<form class="layui-form" action="javascript:void(0);">
			<div class="layui-form-item">
				<img class="logo" src="admin/images/logo.png" alt="系统Logo" />
				<div class="title">四方支付系统</div>
				<div class="desc">
					专业 · 安全 · 高效的支付管理平台
				</div>
			</div>
			<div class="layui-form-item">
				<input type="text" name="username" placeholder="请输入管理员账户" lay-verify="required" class="layui-input" autocomplete="username" />
			</div>
			<div class="layui-form-item">
				<input type="password" name="password" placeholder="请输入登录密码" lay-verify="required" class="layui-input" autocomplete="current-password" />
			</div>
			<div class="layui-form-item">
				<input type="text" name="captcha" placeholder="请输入验证码" lay-verify="required" class="code layui-input" />
				<img src="admin/images/captcha.gif" class="codeImage" alt="验证码" title="点击刷新验证码" />
			</div>
			<div class="layui-form-item">
				<input type="checkbox" name="remember" title="记住登录状态" lay-skin="primary" checked>
			</div>
			<div class="layui-form-item">
				<button type="button" class="pear-btn pear-btn-success login" lay-submit lay-filter="login">
					安全登录
				</button>
			</div>
		</form>
		<!-- 资 源 引 入 -->
		<script src="component/layui/layui.js"></script>
		<script src="component/pear/pear.js"></script>
		<script>
			layui.use(['form', 'button', 'popup'], function() {
				var form = layui.form;
				var button = layui.button;
				var popup = layui.popup;
				
                // 登 录 提 交
				form.on('submit(login)', function(data) {
					
					/// 验证
					
					/// 登录
					
					/// 动画
					button.load({
						elem: '.login',
						time: 1500,
						done: function() {
							popup.success("登录成功", function() {
								location.href = "index.html"
							});
						}
					})
					return false;
				});
			})
		</script>
	</body>
</html>
