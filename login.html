<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="utf-8">
		<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
		<title>四方支付系统 - 管理员登录</title>
		<!-- 样 式 文 件 -->
		<link rel="stylesheet" href="component/pear/css/pear.css" />
		<link rel="stylesheet" href="admin/css/other/login.css" />
	</head>
    <!-- 代 码 结 构 -->
	<body>
		<!-- 动态背景 -->
		<div class="cyber-bg">
			<div class="grid-overlay"></div>
			<div class="floating-particles"></div>
		</div>

		<form class="layui-form cyber-form" action="javascript:void(0);">
			<div class="layui-form-item brand-section">
				<div class="logo-container">
					<div class="logo-glow"></div>
					<img class="logo" src="admin/images/logo.png" />
				</div>
				<div class="title">四方支付系统</div>
				<div class="desc">
					SECURE · FAST · RELIABLE
				</div>
			</div>
			<div class="layui-form-item input-group">
				<div class="input-wrapper">
					<i class="input-icon">👤</i>
					<input type="text" name="username" placeholder="管理员账户" lay-verify="required" class="layui-input cyber-input" />
					<div class="input-glow"></div>
				</div>
			</div>
			<div class="layui-form-item input-group">
				<div class="input-wrapper">
					<i class="input-icon">🔒</i>
					<input type="password" name="password" placeholder="登录密码" lay-verify="required" class="layui-input cyber-input" />
					<div class="input-glow"></div>
				</div>
			</div>
			<div class="layui-form-item input-group captcha-group">
				<div class="input-wrapper captcha-input">
					<i class="input-icon">🛡️</i>
					<input type="text" name="captcha" placeholder="验证码" lay-verify="required" class="code layui-input cyber-input" />
					<div class="input-glow"></div>
				</div>
				<div class="captcha-image-wrapper">
					<img src="admin/images/captcha.gif" class="codeImage cyber-captcha" />
					<div class="captcha-refresh">🔄</div>
				</div>
			</div>
			<div class="layui-form-item checkbox-group">
				<label class="cyber-checkbox">
					<input type="checkbox" name="remember" checked>
					<span class="checkmark"></span>
					<span class="checkbox-text">记住登录状态</span>
				</label>
			</div>
			<div class="layui-form-item">
				<button type="button" class="cyber-btn login" lay-submit lay-filter="login">
					<span class="btn-text">安全登录</span>
					<div class="btn-glow"></div>
				</button>
			</div>
		</form>
		<!-- 资 源 引 入 -->
		<script src="component/layui/layui.js"></script>
		<script src="component/pear/pear.js"></script>
		<script>
			layui.use(['form', 'button', 'popup'], function() {
				var form = layui.form;
				var button = layui.button;
				var popup = layui.popup;
				
                // 登 录 提 交
				form.on('submit(login)', function(data) {
					
					/// 验证
					
					/// 登录
					
					/// 动画
					button.load({
						elem: '.login',
						time: 1500,
						done: function() {
							popup.success("登录成功", function() {
								location.href = "index.html"
							});
						}
					})
					return false;
				});
			})
		</script>
	</body>
</html>
