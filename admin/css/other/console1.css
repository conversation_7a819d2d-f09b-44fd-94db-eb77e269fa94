/* ===== 四方支付系统 - 暗黑科技风格控制台 ===== */

.cyber-dashboard {
	background: #0a0a0a;
	color: #ffffff;
	font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
	margin: 0;
	padding: 0;
}

/* 科技感统计卡片 */
.cyber-stat-card {
	background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
	border: 1px solid rgba(0, 212, 255, 0.3);
	border-radius: 15px;
	overflow: hidden;
	transition: all 0.3s ease;
	position: relative;
	box-shadow: 0 5px 25px rgba(0, 212, 255, 0.1);
}

.cyber-stat-card::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 3px;
	background: linear-gradient(90deg, #00d4ff, #4ecdc4, #ffe66d, #ff6b6b);
	animation: borderGlow 3s linear infinite;
}

@keyframes borderGlow {
	0% { transform: translateX(-100%); }
	100% { transform: translateX(100%); }
}

.cyber-stat-card:hover {
	transform: translateY(-5px);
	box-shadow: 0 15px 40px rgba(0, 212, 255, 0.3);
	border-color: #00d4ff;
}

/* 卡片头部 */
.cyber-stat-card .layui-card-header {
	background: transparent;
	border-bottom: 1px solid rgba(0, 212, 255, 0.2);
	padding: 15px 20px;
	display: flex;
	align-items: center;
	gap: 12px;
}

.card-icon {
	font-size: 24px;
	filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.5));
}

.card-info {
	flex: 1;
}

.card-title {
	font-size: 14px;
	font-weight: 600;
	color: #ffffff;
	margin-bottom: 2px;
}

.card-subtitle {
	font-size: 11px;
	color: #888;
	text-transform: uppercase;
	letter-spacing: 1px;
}

/* 卡片内容 */
.cyber-stat-card .layui-card-body {
	padding: 20px;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.stat-value {
	font-size: 32px;
	font-weight: 700;
	color: #00d4ff;
	text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
	animation: valueGlow 2s ease-in-out infinite alternate;
}

@keyframes valueGlow {
	0% { text-shadow: 0 0 20px rgba(0, 212, 255, 0.5); }
	100% { text-shadow: 0 0 30px rgba(0, 212, 255, 0.8); }
}

.stat-trend {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 4px;
}

.trend-icon {
	font-size: 16px;
}

.trend-text {
	font-size: 12px;
	font-weight: 600;
	color: #4ecdc4;
}

/* 不同类型卡片的配色 */
.payment-card {
	border-left: 4px solid #00d4ff;
}

.payment-card .card-icon {
	filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.7));
}

.success-card {
	border-left: 4px solid #4ecdc4;
}

.success-card .card-icon {
	filter: drop-shadow(0 0 10px rgba(78, 205, 196, 0.7));
}

.success-card .stat-value {
	color: #4ecdc4;
	text-shadow: 0 0 20px rgba(78, 205, 196, 0.5);
}

.warning-card {
	border-left: 4px solid #ffe66d;
}

.warning-card .card-icon {
	filter: drop-shadow(0 0 10px rgba(255, 230, 109, 0.7));
}

.warning-card .stat-value {
	color: #ffe66d;
	text-shadow: 0 0 20px rgba(255, 230, 109, 0.5);
}

.info-card {
	border-left: 4px solid #ff6b6b;
}

.info-card .card-icon {
	filter: drop-shadow(0 0 10px rgba(255, 107, 107, 0.7));
}

.info-card .stat-value {
	color: #ff6b6b;
	text-shadow: 0 0 20px rgba(255, 107, 107, 0.5);
}

.card {
	width: 100%;
	height: 160px;
	background-color: whitesmoke;
	border-radius: 4px;
}

.card .header .avatar {
	width: 28px;
	height: 28px;
	margin: 20px;
	border-radius: 50px;
}

.card .header {
	color: dimgray;
}

.card .body {
	color: gray;
}

.card .body {
	margin-left: 20px;
	margin-right: 20px;
}

.card .footer {
	margin-left: 20px;
	margin-right: 20px;
	margin-top: 20px;
	font-size: 13px;
	color: gray;
	position: absolute;
}

.custom-tab .layui-tab-title {
	border-bottom-width: 0px;
	border-bottom-style: none;
}

.custom-tab .layui-tab-title li {
	margin-left: 10px;
}

.list .list-item {
	height: 31.8px;
	line-height: 31.8px;
	color: gray;
	padding: 5px;
	padding-left: 15px;
	border-radius: 4px;
	margin-top: 5.2px;
}

.list .list-item:hover {
	background-color: whitesmoke;
}

.list .list-item .title {
	font-size: 13px;
	width: 100%;
}

.list .list-item .footer {
	position: absolute;
	right: 30px;
	font-size: 12px;
}

.top-panel-tips i {
	font-size: 33px;
}

.layuiadmin-card-status {
	padding: 0 10px 10px;
}

.layuiadmin-card-status dd {
	padding: 15px 0;
	border-bottom: 1px solid #EEE;
	display: -webkit-flex;
	display: flex;
}

.layuiadmin-card-status dd div.layui-status-img,
.layuiadmin-card-team .layui-team-img {
	width: 32px;
	height: 32px;
	border-radius: 50%;
	margin-right: 15px;
}

.layuiadmin-card-status dd div.layui-status-img a {
	width: 100%;
	height: 100%;
	display: inline-block;
	text-align: center;
	line-height: 32px;
}

.layuiadmin-card-status dd div span {
	color: #BBB;
}

.layuiadmin-card-status dd div a {
	color: #01AAED;
}

.top-panel-tips svg {
	margin-top: -12px;
	width: 50px;
	height: 50px;
}
