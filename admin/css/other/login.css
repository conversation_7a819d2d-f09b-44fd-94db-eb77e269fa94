/* ===== 四方支付系统 - 登录页面样式 ===== */

:root {
	--primary-color: #1a73e8;
	--primary-hover: #1557b0;
	--primary-light: #e8f0fe;
	--success-color: #34a853;
	--error-color: #ea4335;
	--background-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	--surface-color: #ffffff;
	--text-primary: #202124;
	--text-secondary: #5f6368;
	--border-color: #dadce0;
	--shadow-light: 0 1px 3px rgba(60,64,67,0.3);
	--shadow-medium: 0 4px 6px rgba(60,64,67,0.15);
	--shadow-heavy: 0 8px 25px rgba(60,64,67,0.12);
	--border-radius: 8px;
	--border-radius-large: 12px;
}

.layui-form {
	width: 420px !important;
	margin: auto !important;
	margin-top: 10vh !important;
	background: var(--surface-color);
	padding: 48px 40px !important;
	border-radius: var(--border-radius-large) !important;
	box-shadow: var(--shadow-heavy);
	backdrop-filter: blur(10px);
	border: 1px solid rgba(255, 255, 255, 0.2);
}

.layui-form button {
	width: 100% !important;
	height: 48px !important;
	line-height: 48px !important;
	font-size: 16px !important;
	font-weight: 600 !important;
	background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%) !important;
	border: none !important;
	border-radius: var(--border-radius) !important;
	color: white !important;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
	box-shadow: var(--shadow-medium);
}

.layui-form button:hover {
	transform: translateY(-2px) !important;
	box-shadow: 0 8px 25px rgba(26, 115, 232, 0.3) !important;
}

.layui-form-checked[lay-skin=primary] i {
	border-color: var(--primary-color) !important;
	background-color: var(--primary-color) !important;
	color: #fff !important;
}

.layui-tab-content {
	margin-top: 24px !important;
	padding-left: 0px !important;
	padding-right: 0px !important;
}

.layui-form-item {
	margin-bottom: 24px !important;
}

.layui-input {
	height: 48px !important;
	line-height: 48px !important;
	padding: 0 16px !important;
	border: 2px solid var(--border-color) !important;
	border-radius: var(--border-radius) !important;
	font-size: 14px !important;
	transition: all 0.3s ease !important;
	background-color: var(--surface-color) !important;
}

.layui-input:focus {
	border-color: var(--primary-color) !important;
	box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.1) !important;
	outline: none !important;
}

.layui-form-danger:focus{
	border-color: var(--error-color) !important;
	box-shadow: 0 0 0 3px rgba(234, 67, 53, 0.1) !important;
}

.logo {
	width: 64px !important;
	height: 64px !important;
	border-radius: var(--border-radius-large) !important;
	background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%) !important;
	padding: 16px !important;
	margin: 0 auto 24px auto !important;
	display: block !important;
}

.title {
	font-size: 28px !important;
	font-weight: 700 !important;
	color: var(--text-primary) !important;
	text-align: center !important;
	margin-bottom: 8px !important;
	letter-spacing: -0.5px !important;
}

.desc {
	width: 100% !important;
	text-align: center !important;
	color: var(--text-secondary) !important;
	font-size: 14px !important;
	margin-bottom: 32px !important;
	line-height: 1.5 !important;
}

body {
	background: var(--background-gradient);
	background-attachment: fixed;
	background-size: cover;
	height: 100vh;
	margin: 0;
	padding: 0;
	font-family: 'Google Sans', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
	overflow: hidden;
	position: relative;
}

body::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
	pointer-events: none;
}

.code {
	width: calc(100% - 120px) !important;
	margin-right: 12px !important;
	display: inline-block !important;
	vertical-align: top !important;
}

.codeImage {
	width: 100px !important;
	height: 48px !important;
	border: 2px solid var(--border-color) !important;
	border-radius: var(--border-radius) !important;
	cursor: pointer !important;
	transition: all 0.3s ease !important;
	display: inline-block !important;
	vertical-align: top !important;
}

.codeImage:hover {
	border-color: var(--primary-color) !important;
	transform: scale(1.05) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
	.layui-form {
		width: 90% !important;
		margin-top: 5vh !important;
		padding: 32px 24px !important;
	}

	.title {
		font-size: 24px !important;
	}
}

@media (max-width: 480px) {
	.layui-form {
		width: 95% !important;
		padding: 24px 20px !important;
	}

	.code {
		width: 100% !important;
		margin-bottom: 12px !important;
	}

	.codeImage {
		width: 100% !important;
		margin-top: 12px !important;
	}
}

/* 动画效果 */
@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(30px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

.layui-form {
	animation: fadeInUp 0.6s ease-out;
}

.layui-form-item {
	animation: fadeInUp 0.6s ease-out;
	animation-fill-mode: both;
}

.layui-form-item:nth-child(1) { animation-delay: 0.1s; }
.layui-form-item:nth-child(2) { animation-delay: 0.2s; }
.layui-form-item:nth-child(3) { animation-delay: 0.3s; }
.layui-form-item:nth-child(4) { animation-delay: 0.4s; }
.layui-form-item:nth-child(5) { animation-delay: 0.5s; }
.layui-form-item:nth-child(6) { animation-delay: 0.6s; }

