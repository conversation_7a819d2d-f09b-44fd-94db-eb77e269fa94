/* ===== 四方支付系统 - 暗黑科技风格登录页面 ===== */

* {
	margin: 0;
	padding: 0;
	box-sizing: border-box;
}

body {
	font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
	background: #0a0a0a;
	color: #ffffff;
	overflow: hidden;
	height: 100vh;
	position: relative;
}

/* 赛博朋克背景 */
.cyber-bg {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: linear-gradient(45deg, #0a0a0a 0%, #1a1a1a 50%, #0a0a0a 100%);
	z-index: -2;
}

.grid-overlay {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-image:
		linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
		linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
	background-size: 50px 50px;
	animation: gridMove 20s linear infinite;
}

.floating-particles {
	position: absolute;
	width: 100%;
	height: 100%;
	background: radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.3) 0%, transparent 50%),
				radial-gradient(circle at 80% 20%, rgba(255, 107, 107, 0.3) 0%, transparent 50%),
				radial-gradient(circle at 40% 40%, rgba(78, 205, 196, 0.3) 0%, transparent 50%);
	animation: particleFloat 15s ease-in-out infinite;
}

@keyframes gridMove {
	0% { transform: translate(0, 0); }
	100% { transform: translate(50px, 50px); }
}

@keyframes particleFloat {
	0%, 100% { opacity: 0.3; transform: scale(1); }
	50% { opacity: 0.6; transform: scale(1.1); }
}

/* 登录表单容器 */
.cyber-form {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 420px;
	background: rgba(26, 26, 26, 0.9);
	backdrop-filter: blur(20px);
	border: 1px solid rgba(0, 212, 255, 0.3);
	border-radius: 20px;
	padding: 40px;
	box-shadow:
		0 0 50px rgba(0, 212, 255, 0.2),
		inset 0 0 50px rgba(0, 212, 255, 0.05);
	animation: formGlow 3s ease-in-out infinite alternate;
}

@keyframes formGlow {
	0% { box-shadow: 0 0 50px rgba(0, 212, 255, 0.2), inset 0 0 50px rgba(0, 212, 255, 0.05); }
	100% { box-shadow: 0 0 80px rgba(0, 212, 255, 0.4), inset 0 0 50px rgba(0, 212, 255, 0.1); }
}

/* 品牌区域 */
.brand-section {
	text-align: center;
	margin-bottom: 40px;
}

.logo-container {
	position: relative;
	display: inline-block;
	margin-bottom: 20px;
}

.logo-glow {
	position: absolute;
	top: -10px;
	left: -10px;
	right: -10px;
	bottom: -10px;
	background: radial-gradient(circle, rgba(0, 212, 255, 0.4) 0%, transparent 70%);
	border-radius: 50%;
	animation: logoGlow 2s ease-in-out infinite alternate;
}

.logo {
	width: 80px;
	height: 80px;
	border-radius: 50%;
	border: 2px solid #00d4ff;
	position: relative;
	z-index: 1;
	filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.5));
}

@keyframes logoGlow {
	0% { transform: scale(1); opacity: 0.5; }
	100% { transform: scale(1.1); opacity: 0.8; }
}

.title {
	font-size: 28px;
	font-weight: 700;
	color: #00d4ff;
	margin-bottom: 8px;
	text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
	letter-spacing: 2px;
}

.desc {
	font-size: 14px;
	color: #b0b0b0;
	letter-spacing: 4px;
	font-weight: 300;
}

/* 输入框组 */
.input-group {
	margin-bottom: 25px;
}

.input-wrapper {
	position: relative;
	display: flex;
	align-items: center;
}

.input-icon {
	position: absolute;
	left: 15px;
	font-size: 18px;
	z-index: 2;
	filter: drop-shadow(0 0 5px rgba(0, 212, 255, 0.5));
}

.cyber-input {
	width: 100%;
	height: 50px;
	background: rgba(42, 42, 42, 0.8);
	border: 1px solid rgba(0, 212, 255, 0.3);
	border-radius: 10px;
	padding: 0 20px 0 50px;
	color: #ffffff;
	font-size: 16px;
	transition: all 0.3s ease;
	position: relative;
	z-index: 1;
}

.cyber-input::placeholder {
	color: #666;
}

.cyber-input:focus {
	outline: none;
	border-color: #00d4ff;
	background: rgba(42, 42, 42, 1);
	box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

.input-glow {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	border-radius: 10px;
	background: linear-gradient(45deg, transparent, rgba(0, 212, 255, 0.1), transparent);
	opacity: 0;
	transition: opacity 0.3s ease;
	pointer-events: none;
}

.cyber-input:focus + .input-glow {
	opacity: 1;
}

/* 验证码组 */
.captcha-group {
	display: flex;
	gap: 15px;
	align-items: center;
}

.captcha-input {
	flex: 1;
}

.captcha-image-wrapper {
	position: relative;
	width: 120px;
	height: 50px;
}

.cyber-captcha {
	width: 100%;
	height: 100%;
	border: 1px solid rgba(0, 212, 255, 0.3);
	border-radius: 10px;
	cursor: pointer;
	transition: all 0.3s ease;
}

.cyber-captcha:hover {
	border-color: #00d4ff;
	box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
}

.captcha-refresh {
	position: absolute;
	top: -8px;
	right: -8px;
	width: 24px;
	height: 24px;
	background: #00d4ff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 12px;
	cursor: pointer;
	transition: all 0.3s ease;
	animation: rotate 2s linear infinite;
}

@keyframes rotate {
	from { transform: rotate(0deg); }
	to { transform: rotate(360deg); }
}

/* 复选框 */
.checkbox-group {
	margin-bottom: 30px;
}

.cyber-checkbox {
	display: flex;
	align-items: center;
	cursor: pointer;
	user-select: none;
}

.cyber-checkbox input {
	display: none;
}

.checkmark {
	width: 20px;
	height: 20px;
	border: 2px solid rgba(0, 212, 255, 0.5);
	border-radius: 4px;
	margin-right: 12px;
	position: relative;
	transition: all 0.3s ease;
}

.cyber-checkbox input:checked + .checkmark {
	background: #00d4ff;
	border-color: #00d4ff;
	box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.cyber-checkbox input:checked + .checkmark::after {
	content: '✓';
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	color: #0a0a0a;
	font-weight: bold;
	font-size: 14px;
}

.checkbox-text {
	color: #b0b0b0;
	font-size: 14px;
}

/* 登录按钮 */
.cyber-btn {
	width: 100%;
	height: 55px;
	background: linear-gradient(45deg, #00d4ff, #0099cc);
	border: none;
	border-radius: 12px;
	color: #0a0a0a;
	font-size: 18px;
	font-weight: 700;
	cursor: pointer;
	position: relative;
	overflow: hidden;
	transition: all 0.3s ease;
	text-transform: uppercase;
	letter-spacing: 1px;
}

.cyber-btn:hover {
	transform: translateY(-2px);
	box-shadow: 0 10px 30px rgba(0, 212, 255, 0.4);
}

.cyber-btn:active {
	transform: translateY(0);
}

.btn-text {
	position: relative;
	z-index: 2;
}

.btn-glow {
	position: absolute;
	top: 0;
	left: -100%;
	width: 100%;
	height: 100%;
	background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
	transition: left 0.5s ease;
}

.cyber-btn:hover .btn-glow {
	left: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
	.cyber-form {
		width: 90%;
		padding: 30px 20px;
	}

	.captcha-group {
		flex-direction: column;
		gap: 15px;
	}

	.captcha-input {
		width: 100%;
	}

	.captcha-image-wrapper {
		width: 100%;
	}
}

@media (max-width: 480px) {
	.cyber-form {
		width: 95%;
		padding: 25px 15px;
	}

	.title {
		font-size: 24px;
	}

	.desc {
		letter-spacing: 2px;
	}
}

