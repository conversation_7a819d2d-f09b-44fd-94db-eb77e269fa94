/* ===== 四方支付系统 - 暗黑科技风格主框架 ===== */

html,
body,
.layui-layout {
	height: 100%;
	font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
	background: #0a0a0a;
	color: #ffffff;
}

.pear-admin .layui-header,
.pear-admin .layui-body,
.pear-admin .layui-logo,
.pear-admin .layui-side,
.pear-admin .layui-header .layui-layout-left {
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 全局暗黑主题 */
.pear-admin {
	background: #0a0a0a;
}

.pear-admin.banner-layout .layui-side {
	top: 60px!important;
}

.pear-admin.banner-layout .layui-side .layui-logo {
	display: none;
}

.pear-admin.banner-layout .layui-header .layui-logo {
	display: inline-block;
}

.pear-admin.banner-layout .layui-side .layui-side-scroll {
	height: 100%!important;
}

.pear-admin.banner-layout .layui-side .layui-side-scroll {
	height: 100%!important;
}

.pear-admin .layui-header.dark-theme .layui-layout-control .layui-this *{
	background-color: rgba(0,0,0,.1)!important;
}

.pear-admin.banner-layout .layui-header {
	z-index: 99999;
	width: 100%;
	left: 0px;
}

.pear-admin.banner-layout .layui-header .layui-layout-left {
	left: 230px;
}

.pear-admin.banner-layout .layui-header .layui-logo .title {
	top: 2px;
}

.pear-admin.banner-layout .layui-header .layui-layout-control {
	display: inline-block;
	left: 370px;
}

.pear-admin.banner-layout .layui-header.dark-theme {
	box-shadow: 2px 0 6px rgb(0 21 41 / 35%);
}

.pear-admin .layui-header .layui-logo {
	display: none;
}

.pear-admin .layui-logo .title {
	font-size: 20px;
}

.pear-admin .layui-layout-right .layui-nav-child {
	border: 1px solid whitesmoke;
	border-radius: 4px;
	width: auto;
	left: auto;
	right: -23px;
}

.pear-admin .layui-header {
	left: 260px;
	width: calc(100% - 260px);
	background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
	border-bottom: 1px solid rgba(0, 212, 255, 0.2);
	box-shadow: 0 2px 20px rgba(0, 212, 255, 0.1);
	height: 60px;
	backdrop-filter: blur(10px);
}

.pear-admin .layui-layout-control {
	left: 140px;
	position: absolute;
}

.pear-admin .layui-layout-control .layui-nav {
	padding: 0px;
}

.pear-admin .layui-logo {
	width: 260px;
	height: 60px;
	line-height: 60px;
	position: relative;
	background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
	border-bottom: 1px solid rgba(0, 212, 255, 0.3);
	display: flex;
	align-items: center;
	padding: 0 20px;
	box-shadow: 0 2px 15px rgba(0, 212, 255, 0.3);
}

.pear-admin .layui-logo img {
	width: 36px;
	height: 36px;
	border-radius: 8px;
	filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
}

.pear-admin .layui-logo .title {
	font-size: 18px;
	font-weight: 700;
	color: #0a0a0a;
	margin-left: 12px;
	letter-spacing: 1px;
	text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.pear-admin .layui-logo .logo {
	display: none;
}

.pear-admin .layui-side {
	top: 0px;
	width: 260px;
	background: linear-gradient(180deg, #1a1a1a 0%, #0f0f0f 100%);
	box-shadow: 2px 0 20px rgba(0, 212, 255, 0.2);
	z-index: 9999;
	border-right: 1px solid rgba(0, 212, 255, 0.2);
}

.pear-admin .layui-side-scroll::-webkit-scrollbar {
	width: 0px;
	height: 0px;
}

.pear-admin .layui-side-scroll {
	height: calc(100% - 60px) !important;
	background: transparent;
	width: 260px;
	padding: 20px 0;
}

.pear-admin .layui-header .layui-nav .layui-nav-item>a {
	color: #ffffff;
	font-size: 14px;
	font-weight: 500;
	padding: 0 15px;
	border-radius: 8px;
	transition: all 0.3s ease;
	position: relative;
}

.pear-admin .layui-header .layui-nav .layui-nav-item>a:hover {
	background: rgba(0, 212, 255, 0.1);
	color: #00d4ff;
	box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
}

.pear-admin .layui-body {
	left: 260px;
	bottom: 0px;
	padding: 20px;
	background: #0a0a0a;
}

.pear-admin .layui-layout-left {
	left: 0px;
}

/* 侧边栏菜单样式 */
.layui-nav-tree .layui-nav-item a {
	color: #b0b0b0 !important;
	padding: 12px 20px !important;
	margin: 4px 15px !important;
	border-radius: 10px !important;
	transition: all 0.3s ease !important;
	font-weight: 500 !important;
	font-size: 14px !important;
	position: relative !important;
	border: 1px solid transparent !important;
}

.layui-nav-tree .layui-nav-item a:hover {
	background: rgba(0, 212, 255, 0.1) !important;
	color: #00d4ff !important;
	border-color: rgba(0, 212, 255, 0.3) !important;
	box-shadow: 0 0 15px rgba(0, 212, 255, 0.2) !important;
	transform: translateX(5px) !important;
}

.layui-nav-tree .layui-this a {
	background: linear-gradient(135deg, rgba(0, 212, 255, 0.2) 0%, rgba(0, 212, 255, 0.1) 100%) !important;
	color: #00d4ff !important;
	border-color: #00d4ff !important;
	box-shadow: 0 0 20px rgba(0, 212, 255, 0.3) !important;
}

.layui-nav-tree .layui-nav-child {
	background: rgba(15, 15, 15, 0.9) !important;
	border-radius: 10px !important;
	margin: 4px 15px !important;
	border: 1px solid rgba(0, 212, 255, 0.2) !important;
	backdrop-filter: blur(10px) !important;
}

.layui-nav-tree .layui-nav-child a {
	color: #888 !important;
	padding: 8px 16px 8px 35px !important;
	margin: 2px 10px !important;
	font-size: 13px !important;
	border: none !important;
}

.layui-nav-tree .layui-nav-child a:hover {
	color: #00d4ff !important;
	background: rgba(0, 212, 255, 0.05) !important;
	transform: translateX(3px) !important;
}

.pear-admin .layui-footer {
	position: absolute;
    display: flex;
    justify-content: space-between;
	left: 260px;
	background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
	border-top: 1px solid rgba(0, 212, 255, 0.2);
	box-shadow: 0 -2px 20px rgba(0, 212, 255, 0.1);
	-webkit-transition: left .3s;
	transition: left .3s;
	overflow: hidden;
	color: #b0b0b0;
	font-weight: 400;
	font-size: 13px;
	padding: 0 20px;
	height: 50px;
	line-height: 50px;
}

.pear-admin .layui-footer.close {
	display: none;
}

@media screen and (max-width: 768px) {
  .pear-admin.banner-layout .layui-header .layui-logo {
      display: none;
  }
  .pear-admin.banner-layout .layui-header .layui-layout-left {
      left: 0px;
  }
}

/** 收缩布局 */
.pear-mini .layui-side .layui-logo .title {
	display: none;
}

.pear-mini .layui-side .layui-logo .logo {
	display: inline-block;
}

.pear-mini .layui-side {
	width: 60px;
}

.pear-mini .layui-header {
	left: 60px;
	width: calc(100% - 60px);
}

.pear-mini .layui-body {
	left: 60px;
}

.pear-mini .layui-side .layui-logo {
	width: 60px;
}

.pear-mini .layui-footer {
	left: 60px;
}

.pear-mini .layui-nav-tree .layui-nav-item span {
	display: none;
}

.pear-mini .bottom-nav li {
	width: 100% !important;
}

.pear-mini .layui-side-scroll {
	height: calc(100% - 60px);
}

.pear-admin .layui-header .layui-nav .layui-nav-bar {
	top: 0px !important;
	height: 2px !important;
	background-color: #5FB878;
}

.pear-admin .layui-header .layui-nav .layui-this:after {
	display: none;
}

.pear-admin .layui-header .layui-nav-more {
	display: none;
}

.pear-collapsed-pe {
	display: none;
	width: 50px;
	position: absolute;
	z-index: 400000;
	bottom: 30px;
	right: 30px;
	background-color: #5FB878 !important;
	height: 50px;
	line-height: 50px;
	text-align: center;
	border-radius: 50px;
	box-shadow: 2px 0 6px rgba(0, 21, 41, .35);
}

.pear-collapsed-pe a {
	color: white !important;
}

@media screen and (min-width: 768px) {
	.layui-hide-sm {
		display: inline-block !important;
	}
}

@media screen and (min-width: 769px) {
	.layui-hide-sm {
		display: none !important;
	}
}

/** 新增兼容 */
@media screen and (max-width:768px) {
	.collapse {
		display: none !important;
	}

	.pear-collapsed-pe {
		display: inline-block !important;
	}

	.layui-layout-control {
		left: 45px !important;
	}

	.layui-layout-left {
		padding-left: 10px;
		padding-right: 10px;
	}

	.pear-mini .bottom-nav {
		display: none;
	}

	.pear-mini .layui-side-scroll {
		height: calc(100% - 62px);
	}

	.pear-mini .layui-side {
		width: 0px;
	}

	.pear-mini .layui-header {
		left: 0px;
		width: 100%;
	}

	.pear-mini .layui-body {
		left: 0px;
	}

	.pear-mini .layui-footer {
		left: 0px;
	}

	.pear-mini .layui-logo {
		width: 0px;
	}

	.pear-admin .layui-body {
		left: 0px;
	}

	.pear-admin .layui-header {
		left: 0px;
		width: 100%;
	}

	.pear-admin .pear-cover {
		width: 100%;
		height: 100%;
		background-color: #1E1E1E;
		display: block;
		position: absolute;
		z-index: 1000;
		opacity: 0;
		margin-top: -60px;
	}

	.pear-mini .pear-cover {
		display: none;
	}
}

@-webkit-keyframes am-horizontal-roll_show {
	0% {
		opacity: 1;
		-webkit-transform: translateX(2000px);
		transform: translateX(2000px)
	}

	100% {
		opacity: 1;
		-webkit-transform: translateX(0);
		transform: translateX(0)
	}
}

@keyframes am-horizontal-roll_show {
	0% {
		opacity: 1;
		-webkit-transform: translateX(800px);
		-ms-transform: translateX(800px);
		transform: translateX(800px)
	}

	100% {
		opacity: 1;
		-webkit-transform: translateX(0);
		-ms-transform: translateX(0);
		transform: translateX(0)
	}
}

.layer-anim-right {
	-webkit-animation: am-horizontal-roll_show .6s ease-out;
	animation: am-horizontal-roll_show .6s ease-out;

}

/** 侧边主题 (亮) */
.light-theme.layui-side {
	box-shadow: 2px 0 8px 0 rgba(29, 35, 41, .05) !important;
}

.light-theme.layui-side .layui-logo {
	background-color: white !important;
	color: black !important;
	border-bottom: 1px whitesmoke solid;
}

.light-theme.layui-side .layui-side-scroll {
	background-color: white !important;
	color: black !important;
}

.dark-theme.layui-header {
	border-bottom: none;
	background-color: #28333E;
	color: whitesmoke;
}

.dark-theme.layui-header li>a{
	color: whitesmoke!important;
}

.dark-theme.layui-header .layui-logo {
	box-shadow: none;
	border: none;
}

/** 顶部主题 (白) */
.light-theme.layui-header .layui-logo {
	background-color: white;
	border: none;
	box-shadow: none;
}

/** 主题面板 */
.pearone-color .set-text {
	height: 42px;
	line-height: 42px;
}

.pearone-color .color-title {
	padding: 15px 0 0px 20px;
	margin-bottom: 4px;
}

.pearone-color .color-content {
	padding: 15px 10px 0 20px;
}

.pearone-color .color-content ul {
	list-style: none;
	padding: 0px;
}

.pearone-color .color-content ul li {
	position: relative;
	display: inline-block;
	vertical-align: top;
	width: 70px;
	height: 50px;
	margin: 0 20px 20px 0;
	padding: 2px 2px 2px 2px;
	background-color: #f2f2f2;
	cursor: pointer;
	font-size: 12px;
	color: #666;
}

.pearone-color .color-content li.layui-this:after,
.pearone-color .color-content li:hover:after {
	width: 100%;
	height: 100%;
	padding: 4px;
	top: -5px;
	left: -5px;
	border: #5FB878 2px solid;
	opacity: 1;
	border-radius: 4px;
}

.pearone-color .color-content li:after {
	content: '';
	position: absolute;
	z-index: 20;
	top: 50%;
	left: 50%;
	width: 1px;
	height: 0;
	border: 2px solid #F2F2F2;
	transition: all .3s;
	-webkit-transition: all .3s;
	opacity: 0;
}
@media screen and (max-width: 450px) {
  .pearone-color .layui-form-item .layui-input-inline {
      float: left !important;
      width: 190px !important;
      margin: 0 10px 0 0!important;
  }
}
.select-color {
	margin-bottom: 30px;
}

.select-color .select-color-title {
	padding: 15px 0 0px 20px;
	margin-bottom: 4px;
}

.select-color .select-color-content {
	padding: 20px 0 0px 0px;
	margin-bottom: 4px;
}

.select-color .select-color-content .select-color-item {
	background-color: gray;
	width: 30px;
	height: 30px;
	border-radius: 3px;
	float: left;
	margin-left: 20px;
	color: white;
	font-size: 18px;
	text-align: center;
	box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .15);
	line-height: 30px;
}

.message .layui-tab-title li:not(:last-child) {
	border-right: 1px solid #eee;
}

/* 搜索面板 */
.menu-search-content .layui-input {
	padding-left: 30px;
}

.menu-search-content {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
}

.menu-search-input-wrapper {
	width: 100%;
	padding: 15px 15px;
}

.menu-search-no-data {
	display: flex;
	justify-content: center;
	width: 100%;
	height: 122px;
	align-items: center;
}

.menu-search-list {
	width: 100%;
	padding: 5px 15px;
}

.menu-search-list li {
	position: relative;
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: nowrap;
	height: 50px;
	margin-bottom: 8px;
	padding: 0px 10px;
	color: currentColor;
	font-size: 14px;
	border-radius: 4px;
	box-shadow: 0 1px 3px #d4d9e1;
	cursor: pointer;
	background-color: #fff;
}

.menu-search-list li:hover {
	background-color: #5FB878;
	color: white;
}

.menu-search-list li.this {
	background-color: #5FB878;
	color: white;
}

/* 搜索面板结束 */