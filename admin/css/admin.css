/* ===== 四方支付系统 - 全新设计风格 ===== */
/* 主要配色方案:
   - 主色调: #1a73e8 (专业蓝)
   - 辅助色: #34a853 (成功绿)
   - 警告色: #fbbc04 (警告黄)
   - 错误色: #ea4335 (错误红)
   - 背景色: #f8f9fa (浅灰)
   - 文字色: #202124 (深灰)
   - 边框色: #dadce0 (边框灰)
*/

:root {
	--primary-color: #1a73e8;
	--primary-hover: #1557b0;
	--primary-light: #e8f0fe;
	--success-color: #34a853;
	--warning-color: #fbbc04;
	--error-color: #ea4335;
	--background-color: #f8f9fa;
	--surface-color: #ffffff;
	--text-primary: #202124;
	--text-secondary: #5f6368;
	--border-color: #dadce0;
	--border-light: #e8eaed;
	--shadow-light: 0 1px 3px rgba(60,64,67,0.3);
	--shadow-medium: 0 4px 6px rgba(60,64,67,0.15);
	--shadow-heavy: 0 8px 25px rgba(60,64,67,0.12);
	--border-radius: 8px;
	--border-radius-large: 12px;
}

html,
body,
.layui-layout {
	height: 100%;
	font-family: 'Google Sans', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
	background-color: var(--background-color);
	color: var(--text-primary);
}

.pear-admin .layui-header,
.pear-admin .layui-body,
.pear-admin .layui-logo,
.pear-admin .layui-side,
.pear-admin .layui-header .layui-layout-left {
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.pear-admin.banner-layout .layui-side {
	top: 60px!important;
}

.pear-admin.banner-layout .layui-side .layui-logo {
	display: none;
}

.pear-admin.banner-layout .layui-header .layui-logo {
	display: inline-block;
}

.pear-admin.banner-layout .layui-side .layui-side-scroll {
	height: 100%!important;
}

.pear-admin.banner-layout .layui-side .layui-side-scroll {
	height: 100%!important;
}

.pear-admin .layui-header.dark-theme .layui-layout-control .layui-this *{
	background-color: rgba(0,0,0,.1)!important;
}

.pear-admin.banner-layout .layui-header {
	z-index: 99999;
	width: 100%;
	left: 0px;
}

.pear-admin.banner-layout .layui-header .layui-layout-left {
	left: 230px;
}

.pear-admin.banner-layout .layui-header .layui-logo .title {
	top: 2px;
}

.pear-admin.banner-layout .layui-header .layui-layout-control {
	display: inline-block;
	left: 370px;
}

.pear-admin.banner-layout .layui-header.dark-theme {
	box-shadow: 2px 0 6px rgb(0 21 41 / 35%);
}

.pear-admin .layui-header .layui-logo {
	display: none;
}

.pear-admin .layui-logo .title {
	font-size: 20px;
}

.pear-admin .layui-layout-right .layui-nav-child {
	border: 1px solid whitesmoke;
	border-radius: 4px;
	width: auto;
	left: auto;
	right: -23px;
}

.pear-admin .layui-header {
	left: 260px;
	width: calc(100% - 260px);
	background-color: var(--surface-color);
	border-bottom: 1px solid var(--border-light);
	box-shadow: var(--shadow-light);
	height: 64px;
	z-index: 1000;
}

.pear-admin .layui-layout-control {
	left: 140px;
	position: absolute;
}

.pear-admin .layui-layout-control .layui-nav {
	padding: 0px;
}

.pear-admin .layui-logo {
	width: 260px;
	height: 64px;
	line-height: 64px;
	position: relative;
	background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
	display: flex;
	align-items: center;
	padding: 0 20px;
}

.pear-admin .layui-logo img {
	width: 40px;
	height: 40px;
	border-radius: var(--border-radius);
	background-color: rgba(255, 255, 255, 0.1);
	padding: 8px;
}

.pear-admin .layui-logo .title {
	font-size: 20px;
	font-weight: 600;
	color: white;
	margin-left: 12px;
	letter-spacing: 0.5px;
}

.pear-admin .layui-logo .logo {
	display: none;
}

.pear-admin .layui-side {
	top: 0px;
	width: 260px;
	box-shadow: var(--shadow-medium);
	z-index: 9999;
	background: linear-gradient(180deg, var(--primary-color) 0%, var(--primary-hover) 100%);
}

.pear-admin .layui-side-scroll::-webkit-scrollbar {
	width: 0px;
	height: 0px;
}

.pear-admin .layui-side-scroll {
	height: calc(100% - 64px) !important;
	background: transparent;
	width: 260px;
	padding: 16px 0;
}

.pear-admin .layui-header .layui-nav .layui-nav-item>a {
	color: var(--text-primary);
	font-size: 14px;
	font-weight: 500;
	padding: 0 16px;
	border-radius: var(--border-radius);
	transition: all 0.2s ease;
}

.pear-admin .layui-header .layui-nav .layui-nav-item>a:hover {
	background-color: var(--primary-light);
	color: var(--primary-color);
}

.pear-admin .layui-body {
	left: 260px;
	bottom: 0px;
	padding: 24px;
	background-color: var(--background-color);
}

.pear-admin .layui-layout-left {
	left: 0px;
}

.pear-admin .layui-footer {
	position: absolute;
    display: flex;
    justify-content: space-between;
	left: 260px;
	background: var(--surface-color);
	border-top: 1px solid var(--border-light);
	box-shadow: var(--shadow-light);
	-webkit-transition: left .3s;
	transition: left .3s;
	overflow: hidden;
	color: var(--text-secondary);
	font-weight: 400;
	font-size: 13px;
	padding: 0 24px;
	height: 48px;
	line-height: 48px;
}

.pear-admin .layui-footer.close {
	display: none;
}

@media screen and (max-width: 768px) {
  .pear-admin.banner-layout .layui-header .layui-logo {
      display: none;
  }
  .pear-admin.banner-layout .layui-header .layui-layout-left {
      left: 0px;
  }
}

/* ===== 侧边栏菜单样式 ===== */
.layui-nav-tree .layui-nav-item a {
	color: rgba(255, 255, 255, 0.9) !important;
	padding: 12px 20px !important;
	margin: 4px 16px !important;
	border-radius: var(--border-radius) !important;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
	font-weight: 500 !important;
	font-size: 14px !important;
}

.layui-nav-tree .layui-nav-item a:hover {
	background-color: rgba(255, 255, 255, 0.1) !important;
	transform: translateX(4px) !important;
}

.layui-nav-tree .layui-this a {
	background-color: rgba(255, 255, 255, 0.15) !important;
	color: white !important;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

.layui-nav-tree .layui-nav-child {
	background-color: rgba(0, 0, 0, 0.1) !important;
	border-radius: var(--border-radius) !important;
	margin: 4px 16px !important;
}

.layui-nav-tree .layui-nav-child a {
	color: rgba(255, 255, 255, 0.8) !important;
	padding: 8px 16px 8px 32px !important;
	margin: 2px 8px !important;
	font-size: 13px !important;
}

/** 收缩布局 */
.pear-mini .layui-side .layui-logo .title {
	display: none;
}

.pear-mini .layui-side .layui-logo .logo {
	display: inline-block;
}

.pear-mini .layui-side {
	width: 72px;
}

.pear-mini .layui-header {
	left: 72px;
	width: calc(100% - 72px);
}

.pear-mini .layui-body {
	left: 72px;
}

.pear-mini .layui-side .layui-logo {
	width: 72px;
}

.pear-mini .layui-footer {
	left: 72px;
}

.pear-mini .layui-nav-tree .layui-nav-item span {
	display: none;
}

.pear-mini .bottom-nav li {
	width: 100% !important;
}

.pear-mini .layui-side-scroll {
	height: calc(100% - 60px);
}

.pear-admin .layui-header .layui-nav .layui-nav-bar {
	top: 0px !important;
	height: 2px !important;
	background-color: #5FB878;
}

.pear-admin .layui-header .layui-nav .layui-this:after {
	display: none;
}

.pear-admin .layui-header .layui-nav-more {
	display: none;
}

.pear-collapsed-pe {
	display: none;
	width: 50px;
	position: absolute;
	z-index: 400000;
	bottom: 30px;
	right: 30px;
	background-color: #5FB878 !important;
	height: 50px;
	line-height: 50px;
	text-align: center;
	border-radius: 50px;
	box-shadow: 2px 0 6px rgba(0, 21, 41, .35);
}

.pear-collapsed-pe a {
	color: white !important;
}

@media screen and (min-width: 768px) {
	.layui-hide-sm {
		display: inline-block !important;
	}
}

@media screen and (min-width: 769px) {
	.layui-hide-sm {
		display: none !important;
	}
}

/** 新增兼容 */
@media screen and (max-width:768px) {
	.collapse {
		display: none !important;
	}

	.pear-collapsed-pe {
		display: inline-block !important;
	}

	.layui-layout-control {
		left: 45px !important;
	}

	.layui-layout-left {
		padding-left: 10px;
		padding-right: 10px;
	}

	.pear-mini .bottom-nav {
		display: none;
	}

	.pear-mini .layui-side-scroll {
		height: calc(100% - 62px);
	}

	.pear-mini .layui-side {
		width: 0px;
	}

	.pear-mini .layui-header {
		left: 0px;
		width: 100%;
	}

	.pear-mini .layui-body {
		left: 0px;
	}

	.pear-mini .layui-footer {
		left: 0px;
	}

	.pear-mini .layui-logo {
		width: 0px;
	}

	.pear-admin .layui-body {
		left: 0px;
	}

	.pear-admin .layui-header {
		left: 0px;
		width: 100%;
	}

	.pear-admin .pear-cover {
		width: 100%;
		height: 100%;
		background-color: #1E1E1E;
		display: block;
		position: absolute;
		z-index: 1000;
		opacity: 0;
		margin-top: -60px;
	}

	.pear-mini .pear-cover {
		display: none;
	}
}

@-webkit-keyframes am-horizontal-roll_show {
	0% {
		opacity: 1;
		-webkit-transform: translateX(2000px);
		transform: translateX(2000px)
	}

	100% {
		opacity: 1;
		-webkit-transform: translateX(0);
		transform: translateX(0)
	}
}

@keyframes am-horizontal-roll_show {
	0% {
		opacity: 1;
		-webkit-transform: translateX(800px);
		-ms-transform: translateX(800px);
		transform: translateX(800px)
	}

	100% {
		opacity: 1;
		-webkit-transform: translateX(0);
		-ms-transform: translateX(0);
		transform: translateX(0)
	}
}

.layer-anim-right {
	-webkit-animation: am-horizontal-roll_show .6s ease-out;
	animation: am-horizontal-roll_show .6s ease-out;

}

/** 侧边主题 (亮) */
.light-theme.layui-side {
	box-shadow: 2px 0 8px 0 rgba(29, 35, 41, .05) !important;
}

.light-theme.layui-side .layui-logo {
	background-color: white !important;
	color: black !important;
	border-bottom: 1px whitesmoke solid;
}

.light-theme.layui-side .layui-side-scroll {
	background-color: white !important;
	color: black !important;
}

.dark-theme.layui-header {
	border-bottom: none;
	background-color: #28333E;
	color: whitesmoke;
}

.dark-theme.layui-header li>a{
	color: whitesmoke!important;
}

.dark-theme.layui-header .layui-logo {
	box-shadow: none;
	border: none;
}

/** 顶部主题 (白) */
.light-theme.layui-header .layui-logo {
	background-color: white;
	border: none;
	box-shadow: none;
}

/** 主题面板 */
.pearone-color .set-text {
	height: 42px;
	line-height: 42px;
}

.pearone-color .color-title {
	padding: 15px 0 0px 20px;
	margin-bottom: 4px;
}

.pearone-color .color-content {
	padding: 15px 10px 0 20px;
}

.pearone-color .color-content ul {
	list-style: none;
	padding: 0px;
}

.pearone-color .color-content ul li {
	position: relative;
	display: inline-block;
	vertical-align: top;
	width: 70px;
	height: 50px;
	margin: 0 20px 20px 0;
	padding: 2px 2px 2px 2px;
	background-color: #f2f2f2;
	cursor: pointer;
	font-size: 12px;
	color: #666;
}

.pearone-color .color-content li.layui-this:after,
.pearone-color .color-content li:hover:after {
	width: 100%;
	height: 100%;
	padding: 4px;
	top: -5px;
	left: -5px;
	border: #5FB878 2px solid;
	opacity: 1;
	border-radius: 4px;
}

.pearone-color .color-content li:after {
	content: '';
	position: absolute;
	z-index: 20;
	top: 50%;
	left: 50%;
	width: 1px;
	height: 0;
	border: 2px solid #F2F2F2;
	transition: all .3s;
	-webkit-transition: all .3s;
	opacity: 0;
}
@media screen and (max-width: 450px) {
  .pearone-color .layui-form-item .layui-input-inline {
      float: left !important;
      width: 190px !important;
      margin: 0 10px 0 0!important;
  }
}
.select-color {
	margin-bottom: 30px;
}

.select-color .select-color-title {
	padding: 15px 0 0px 20px;
	margin-bottom: 4px;
}

.select-color .select-color-content {
	padding: 20px 0 0px 0px;
	margin-bottom: 4px;
}

.select-color .select-color-content .select-color-item {
	background-color: gray;
	width: 30px;
	height: 30px;
	border-radius: 3px;
	float: left;
	margin-left: 20px;
	color: white;
	font-size: 18px;
	text-align: center;
	box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .15);
	line-height: 30px;
}

.message .layui-tab-title li:not(:last-child) {
	border-right: 1px solid #eee;
}

/* 搜索面板 */
.menu-search-content .layui-input {
	padding-left: 30px;
}

.menu-search-content {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
}

.menu-search-input-wrapper {
	width: 100%;
	padding: 15px 15px;
}

.menu-search-no-data {
	display: flex;
	justify-content: center;
	width: 100%;
	height: 122px;
	align-items: center;
}

.menu-search-list {
	width: 100%;
	padding: 5px 15px;
}

.menu-search-list li {
	position: relative;
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: nowrap;
	height: 50px;
	margin-bottom: 8px;
	padding: 0px 10px;
	color: currentColor;
	font-size: 14px;
	border-radius: 4px;
	box-shadow: 0 1px 3px #d4d9e1;
	cursor: pointer;
	background-color: #fff;
}

.menu-search-list li:hover {
	background-color: #5FB878;
	color: white;
}

.menu-search-list li.this {
	background-color: #5FB878;
	color: white;
}

/* 搜索面板结束 */