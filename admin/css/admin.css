/* ===== 四方支付系统 - 暗黑科技风格主框架 ===== */

html,
body,
.layui-layout {
	height: 100%;
	font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
	background: #0a0a0a;
	color: #ffffff;
}

.pear-admin .layui-header,
.pear-admin .layui-body,
.pear-admin .layui-logo,
.pear-admin .layui-side,
.pear-admin .layui-header .layui-layout-left {
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 全局暗黑主题 */
.pear-admin {
	background: #0a0a0a;
}

.pear-admin.banner-layout .layui-side {
	top: 60px!important;
}

.pear-admin.banner-layout .layui-side .layui-logo {
	display: none;
}

.pear-admin.banner-layout .layui-header .layui-logo {
	display: inline-block;
}

.pear-admin.banner-layout .layui-side .layui-side-scroll {
	height: 100%!important;
}

.pear-admin.banner-layout .layui-side .layui-side-scroll {
	height: 100%!important;
}

.pear-admin .layui-header.dark-theme .layui-layout-control .layui-this *{
	background-color: rgba(0,0,0,.1)!important;
}

.pear-admin.banner-layout .layui-header {
	z-index: 99999;
	width: 100%;
	left: 0px;
}

.pear-admin.banner-layout .layui-header .layui-layout-left {
	left: 230px;
}

.pear-admin.banner-layout .layui-header .layui-logo .title {
	top: 2px;
}

.pear-admin.banner-layout .layui-header .layui-layout-control {
	display: inline-block;
	left: 370px;
}

.pear-admin.banner-layout .layui-header.dark-theme {
	box-shadow: 2px 0 6px rgb(0 21 41 / 35%);
}

.pear-admin .layui-header .layui-logo {
	display: none;
}

.pear-admin .layui-logo .title {
	font-size: 20px;
}

.pear-admin .layui-layout-right .layui-nav-child {
	border: 1px solid whitesmoke;
	border-radius: 4px;
	width: auto;
	left: auto;
	right: -23px;
}

.pear-admin .layui-header {
	left: 260px;
	width: calc(100% - 260px);
	background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
	border-bottom: 2px solid rgba(0, 212, 255, 0.2);
	box-shadow:
		0 4px 20px rgba(0, 0, 0, 0.3),
		inset 0 1px 0 rgba(255, 255, 255, 0.1);
	height: 70px;
	backdrop-filter: blur(15px);
	position: relative;
	z-index: 1000;
}

/* 顶部导航科技感装饰 */
.pear-admin .layui-header::after {
	content: '';
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 2px;
	background: linear-gradient(90deg,
		transparent 0%,
		#00d4ff 25%,
		#4ecdc4 50%,
		#ffe66d 75%,
		transparent 100%);
	animation: headerGlow 4s ease-in-out infinite alternate;
}

@keyframes headerGlow {
	0% { opacity: 0.3; }
	100% { opacity: 0.8; }
}

.pear-admin .layui-layout-control {
	left: 140px;
	position: absolute;
}

.pear-admin .layui-layout-control .layui-nav {
	padding: 0px;
}

.pear-admin .layui-logo {
	width: 260px;
	height: 70px;
	line-height: 70px;
	position: relative;
	background: linear-gradient(135deg, #34495e 0%, #2c3e50 50%, #1a252f 100%);
	border-bottom: 2px solid rgba(0, 212, 255, 0.3);
	display: flex;
	align-items: center;
	padding: 0 20px;
	box-shadow:
		0 4px 20px rgba(0, 0, 0, 0.3),
		inset 0 1px 0 rgba(255, 255, 255, 0.1);
	overflow: hidden;
}

/* Logo区域科技感装饰 */
.pear-admin .layui-logo::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 2px;
	background: linear-gradient(90deg, #00d4ff, #4ecdc4, #ffe66d, #ff6b6b);
	animation: logoTopGlow 3s linear infinite;
}

@keyframes logoTopGlow {
	0% { transform: translateX(-100%); }
	100% { transform: translateX(100%); }
}

.pear-admin .layui-logo img {
	width: 40px;
	height: 40px;
	border-radius: 10px;
	border: 2px solid rgba(0, 212, 255, 0.5);
	filter: drop-shadow(0 0 15px rgba(0, 212, 255, 0.6));
	animation: logoImageGlow 2s ease-in-out infinite alternate;
}

@keyframes logoImageGlow {
	0% {
		border-color: rgba(0, 212, 255, 0.5);
		filter: drop-shadow(0 0 15px rgba(0, 212, 255, 0.6));
	}
	100% {
		border-color: rgba(0, 212, 255, 0.8);
		filter: drop-shadow(0 0 25px rgba(0, 212, 255, 0.9));
	}
}

.pear-admin .layui-logo .title {
	font-size: 18px;
	font-weight: 700;
	color: #ffffff;
	margin-left: 15px;
	letter-spacing: 1px;
	text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
	position: relative;
	z-index: 2;
}

.pear-admin .layui-logo .logo {
	display: none;
}

.pear-admin .layui-side {
	top: 0px;
	width: 260px;
	background: linear-gradient(180deg, #2c3e50 0%, #1a252f 50%, #0f1419 100%);
	box-shadow:
		2px 0 30px rgba(0, 0, 0, 0.5),
		inset -1px 0 0 rgba(0, 212, 255, 0.1);
	z-index: 9999;
	border-right: 1px solid rgba(0, 212, 255, 0.15);
	position: relative;
}

/* 侧边栏科技感装饰 */
.pear-admin .layui-side::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	width: 2px;
	height: 100%;
	background: linear-gradient(180deg,
		transparent 0%,
		#00d4ff 20%,
		#4ecdc4 40%,
		#ffe66d 60%,
		#ff6b6b 80%,
		transparent 100%);
	animation: sidebarGlow 4s ease-in-out infinite alternate;
}

@keyframes sidebarGlow {
	0% { opacity: 0.3; }
	100% { opacity: 0.8; }
}

.pear-admin .layui-side-scroll::-webkit-scrollbar {
	width: 0px;
	height: 0px;
}

.pear-admin .layui-side-scroll {
	height: calc(100% - 70px) !important;
	background: transparent;
	width: 260px;
	padding: 15px 0;
	position: relative;
	z-index: 1;
}

.pear-admin .layui-header .layui-nav .layui-nav-item>a {
	color: #ffffff;
	font-size: 14px;
	font-weight: 500;
	padding: 0 15px;
	border-radius: 8px;
	transition: all 0.3s ease;
	position: relative;
}

.pear-admin .layui-header .layui-nav .layui-nav-item>a:hover {
	background: rgba(0, 212, 255, 0.1);
	color: #00d4ff;
	box-shadow: 0 0 15px rgba(0, 212, 255, 0.3);
}

.pear-admin .layui-body {
	left: 260px;
	top: 70px;
	bottom: 0px;
	padding: 25px;
	background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
	position: relative;
}

/* 主体内容区域科技感背景 */
.pear-admin .layui-body::before {
	content: '';
	position: fixed;
	top: 70px;
	left: 260px;
	right: 0;
	bottom: 0;
	background-image:
		radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.03) 0%, transparent 50%),
		radial-gradient(circle at 80% 20%, rgba(255, 107, 107, 0.03) 0%, transparent 50%),
		radial-gradient(circle at 40% 40%, rgba(78, 205, 196, 0.03) 0%, transparent 50%);
	pointer-events: none;
	z-index: -1;
}

.pear-admin .layui-layout-left {
	left: 0px;
}

/* ===== 暗黑科技风格侧边栏菜单 ===== */

/* 重置layui默认样式 */
.layui-nav-tree {
	background: transparent !important;
}

.layui-nav-tree .layui-nav-item {
	margin: 0 !important;
	border-bottom: none !important;
}

.layui-nav-tree .layui-nav-item a {
	color: rgba(255, 255, 255, 0.85) !important;
	padding: 16px 20px !important;
	margin: 3px 12px !important;
	border-radius: 10px !important;
	transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
	font-weight: 500 !important;
	font-size: 14px !important;
	position: relative !important;
	border: 1px solid transparent !important;
	background: transparent !important;
	display: flex !important;
	align-items: center !important;
	min-height: 50px !important;
	text-decoration: none !important;
	overflow: hidden !important;
}

/* 菜单图标样式 */
.layui-nav-tree .layui-nav-item a .layui-icon {
	margin-right: 14px !important;
	font-size: 17px !important;
	opacity: 0.9 !important;
	transition: all 0.3s ease !important;
	width: 20px !important;
	text-align: center !important;
}

/* 悬停效果 */
.layui-nav-tree .layui-nav-item a:hover {
	background: linear-gradient(135deg, rgba(0, 212, 255, 0.12) 0%, rgba(0, 212, 255, 0.06) 100%) !important;
	color: #00d4ff !important;
	border-color: rgba(0, 212, 255, 0.4) !important;
	box-shadow:
		0 0 25px rgba(0, 212, 255, 0.15),
		inset 0 0 25px rgba(0, 212, 255, 0.08) !important;
	transform: translateX(6px) !important;
}

.layui-nav-tree .layui-nav-item a:hover .layui-icon {
	color: #00d4ff !important;
	opacity: 1 !important;
	text-shadow: 0 0 12px rgba(0, 212, 255, 0.6) !important;
	transform: scale(1.1) !important;
}

/* 选中状态 */
.layui-nav-tree .layui-this a,
.layui-nav-tree .layui-this > a,
.layui-nav-tree .layui-nav-itemed > a {
	background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%) !important;
	color: #0a0a0a !important;
	border-color: #00d4ff !important;
	box-shadow:
		0 0 30px rgba(0, 212, 255, 0.4),
		inset 0 1px 0 rgba(255, 255, 255, 0.2),
		inset 0 -1px 0 rgba(0, 0, 0, 0.1) !important;
	font-weight: 600 !important;
	transform: translateX(8px) !important;
}

.layui-nav-tree .layui-this a .layui-icon,
.layui-nav-tree .layui-this > a .layui-icon,
.layui-nav-tree .layui-nav-itemed > a .layui-icon {
	color: #0a0a0a !important;
	opacity: 1 !important;
	text-shadow: none !important;
	transform: scale(1.1) !important;
}

/* 子菜单样式 */
.layui-nav-tree .layui-nav-child {
	background: rgba(8, 8, 8, 0.95) !important;
	border-radius: 0 0 10px 10px !important;
	margin: 0 12px 8px 12px !important;
	border: 1px solid rgba(0, 212, 255, 0.25) !important;
	border-top: none !important;
	backdrop-filter: blur(15px) !important;
	box-shadow:
		inset 0 0 25px rgba(0, 212, 255, 0.08),
		0 8px 25px rgba(0, 0, 0, 0.3) !important;
	padding: 8px 0 !important;
}

.layui-nav-tree .layui-nav-child a {
	color: rgba(255, 255, 255, 0.7) !important;
	padding: 12px 20px 12px 50px !important;
	margin: 2px 8px !important;
	font-size: 13px !important;
	border: none !important;
	border-radius: 8px !important;
	min-height: 42px !important;
	transition: all 0.3s ease !important;
	position: relative !important;
	display: flex !important;
	align-items: center !important;
}

.layui-nav-tree .layui-nav-child a::before {
	content: '•' !important;
	position: absolute !important;
	left: 35px !important;
	color: rgba(0, 212, 255, 0.6) !important;
	font-size: 16px !important;
	transition: all 0.3s ease !important;
}

.layui-nav-tree .layui-nav-child a:hover {
	color: #00d4ff !important;
	background: rgba(0, 212, 255, 0.1) !important;
	transform: translateX(8px) !important;
	border-color: transparent !important;
	box-shadow: 0 0 20px rgba(0, 212, 255, 0.2) !important;
}

.layui-nav-tree .layui-nav-child a:hover::before {
	color: #00d4ff !important;
	text-shadow: 0 0 8px rgba(0, 212, 255, 0.8) !important;
	transform: scale(1.2) !important;
}

.layui-nav-tree .layui-nav-child .layui-this a {
	background: rgba(0, 212, 255, 0.2) !important;
	color: #00d4ff !important;
	border-left: 4px solid #00d4ff !important;
	font-weight: 600 !important;
	padding-left: 46px !important;
}

.layui-nav-tree .layui-nav-child .layui-this a::before {
	color: #00d4ff !important;
	text-shadow: 0 0 10px rgba(0, 212, 255, 1) !important;
	transform: scale(1.3) !important;
}

/* 展开箭头样式 */
.layui-nav-tree .layui-nav-more {
	border-color: rgba(255, 255, 255, 0.5) transparent transparent !important;
	transition: all 0.3s ease !important;
	right: 20px !important;
}

.layui-nav-tree .layui-nav-mored {
	border-color: transparent transparent rgba(255, 255, 255, 0.5) !important;
	transform: rotate(180deg) !important;
}

.layui-nav-tree .layui-nav-item:hover .layui-nav-more {
	border-top-color: #00d4ff !important;
}

.layui-nav-tree .layui-nav-item:hover .layui-nav-mored {
	border-bottom-color: #00d4ff !important;
}

.layui-nav-tree .layui-this .layui-nav-more,
.layui-nav-tree .layui-nav-itemed .layui-nav-more {
	border-top-color: #0a0a0a !important;
}

.layui-nav-tree .layui-this .layui-nav-mored,
.layui-nav-tree .layui-nav-itemed .layui-nav-mored {
	border-bottom-color: #0a0a0a !important;
}

/* 菜单项左侧科技感装饰线 */
.layui-nav-tree .layui-nav-item > a::before {
	content: '' !important;
	position: absolute !important;
	left: 0 !important;
	top: 50% !important;
	transform: translateY(-50%) !important;
	width: 4px !important;
	height: 0 !important;
	background: linear-gradient(180deg, #00d4ff 0%, #4ecdc4 100%) !important;
	border-radius: 0 3px 3px 0 !important;
	transition: height 0.4s ease !important;
}

.layui-nav-tree .layui-nav-item > a:hover::before {
	height: 70% !important;
}

.layui-nav-tree .layui-this > a::before,
.layui-nav-tree .layui-nav-itemed > a::before {
	height: 85% !important;
	background: linear-gradient(180deg, #ffffff 0%, #e0e0e0 100%) !important;
	width: 5px !important;
}

/* 菜单分组标题样式 */
.layui-nav-tree .nav-group-title {
	color: rgba(255, 255, 255, 0.4) !important;
	font-size: 12px !important;
	font-weight: 600 !important;
	text-transform: uppercase !important;
	letter-spacing: 1px !important;
	padding: 20px 20px 8px 20px !important;
	margin: 0 !important;
	border-bottom: 1px solid rgba(0, 212, 255, 0.1) !important;
	background: transparent !important;
}

/* 滚动条样式 */
.layui-side-scroll::-webkit-scrollbar {
	width: 4px;
}

.layui-side-scroll::-webkit-scrollbar-track {
	background: rgba(0, 0, 0, 0.2);
}

.layui-side-scroll::-webkit-scrollbar-thumb {
	background: linear-gradient(180deg, #00d4ff, #4ecdc4);
	border-radius: 2px;
}

.layui-side-scroll::-webkit-scrollbar-thumb:hover {
	background: linear-gradient(180deg, #4ecdc4, #00d4ff);
}

.pear-admin .layui-footer {
	position: absolute;
    display: flex;
    justify-content: space-between;
	left: 260px;
	background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
	border-top: 2px solid rgba(0, 212, 255, 0.2);
	box-shadow:
		0 -4px 20px rgba(0, 0, 0, 0.3),
		inset 0 1px 0 rgba(255, 255, 255, 0.1);
	-webkit-transition: left .3s;
	transition: left .3s;
	overflow: hidden;
	color: rgba(255, 255, 255, 0.7);
	font-weight: 400;
	font-size: 13px;
	padding: 0 25px;
	height: 55px;
	line-height: 55px;
	position: relative;
}

/* 页脚科技感装饰 */
.pear-admin .layui-footer::before {
	content: '';
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	height: 2px;
	background: linear-gradient(90deg,
		transparent 0%,
		#00d4ff 25%,
		#4ecdc4 50%,
		#ffe66d 75%,
		transparent 100%);
	animation: footerGlow 4s ease-in-out infinite alternate reverse;
}

@keyframes footerGlow {
	0% { opacity: 0.3; }
	100% { opacity: 0.8; }
}

.pear-admin .layui-footer.close {
	display: none;
}

@media screen and (max-width: 768px) {
  .pear-admin.banner-layout .layui-header .layui-logo {
      display: none;
  }
  .pear-admin.banner-layout .layui-header .layui-layout-left {
      left: 0px;
  }
}

/** 收缩布局 */
.pear-mini .layui-side .layui-logo .title {
	display: none;
}

.pear-mini .layui-side .layui-logo .logo {
	display: inline-block;
}

.pear-mini .layui-side {
	width: 70px;
}

.pear-mini .layui-header {
	left: 70px;
	width: calc(100% - 70px);
}

.pear-mini .layui-body {
	left: 70px;
}

.pear-mini .layui-side .layui-logo {
	width: 70px;
	justify-content: center;
}

.pear-mini .layui-footer {
	left: 70px;
}

/* 收缩状态下的菜单样式 */
.pear-mini .layui-nav-tree .layui-nav-item a {
	justify-content: center !important;
	padding: 15px 10px !important;
}

.pear-mini .layui-nav-tree .layui-nav-item a .layui-icon {
	margin-right: 0 !important;
	font-size: 18px !important;
}

.pear-mini .layui-nav-tree .layui-nav-item a span {
	display: none;
}

.pear-mini .layui-nav-tree .layui-nav-item span {
	display: none;
}

.pear-mini .bottom-nav li {
	width: 100% !important;
}

.pear-mini .layui-side-scroll {
	height: calc(100% - 60px);
}

.pear-admin .layui-header .layui-nav .layui-nav-bar {
	top: 0px !important;
	height: 2px !important;
	background-color: #5FB878;
}

.pear-admin .layui-header .layui-nav .layui-this:after {
	display: none;
}

.pear-admin .layui-header .layui-nav-more {
	display: none;
}

.pear-collapsed-pe {
	display: none;
	width: 50px;
	position: absolute;
	z-index: 400000;
	bottom: 30px;
	right: 30px;
	background-color: #5FB878 !important;
	height: 50px;
	line-height: 50px;
	text-align: center;
	border-radius: 50px;
	box-shadow: 2px 0 6px rgba(0, 21, 41, .35);
}

.pear-collapsed-pe a {
	color: white !important;
}

@media screen and (min-width: 768px) {
	.layui-hide-sm {
		display: inline-block !important;
	}
}

@media screen and (min-width: 769px) {
	.layui-hide-sm {
		display: none !important;
	}
}

/** 新增兼容 */
@media screen and (max-width:768px) {
	.collapse {
		display: none !important;
	}

	.pear-collapsed-pe {
		display: inline-block !important;
	}

	.layui-layout-control {
		left: 45px !important;
	}

	.layui-layout-left {
		padding-left: 10px;
		padding-right: 10px;
	}

	.pear-mini .bottom-nav {
		display: none;
	}

	.pear-mini .layui-side-scroll {
		height: calc(100% - 62px);
	}

	.pear-mini .layui-side {
		width: 0px;
	}

	.pear-mini .layui-header {
		left: 0px;
		width: 100%;
	}

	.pear-mini .layui-body {
		left: 0px;
	}

	.pear-mini .layui-footer {
		left: 0px;
	}

	.pear-mini .layui-logo {
		width: 0px;
	}

	.pear-admin .layui-body {
		left: 0px;
	}

	.pear-admin .layui-header {
		left: 0px;
		width: 100%;
	}

	.pear-admin .pear-cover {
		width: 100%;
		height: 100%;
		background-color: #1E1E1E;
		display: block;
		position: absolute;
		z-index: 1000;
		opacity: 0;
		margin-top: -60px;
	}

	.pear-mini .pear-cover {
		display: none;
	}
}

@-webkit-keyframes am-horizontal-roll_show {
	0% {
		opacity: 1;
		-webkit-transform: translateX(2000px);
		transform: translateX(2000px)
	}

	100% {
		opacity: 1;
		-webkit-transform: translateX(0);
		transform: translateX(0)
	}
}

@keyframes am-horizontal-roll_show {
	0% {
		opacity: 1;
		-webkit-transform: translateX(800px);
		-ms-transform: translateX(800px);
		transform: translateX(800px)
	}

	100% {
		opacity: 1;
		-webkit-transform: translateX(0);
		-ms-transform: translateX(0);
		transform: translateX(0)
	}
}

.layer-anim-right {
	-webkit-animation: am-horizontal-roll_show .6s ease-out;
	animation: am-horizontal-roll_show .6s ease-out;

}

/** 侧边主题 (亮) */
.light-theme.layui-side {
	box-shadow: 2px 0 8px 0 rgba(29, 35, 41, .05) !important;
}

.light-theme.layui-side .layui-logo {
	background-color: white !important;
	color: black !important;
	border-bottom: 1px whitesmoke solid;
}

.light-theme.layui-side .layui-side-scroll {
	background-color: white !important;
	color: black !important;
}

.dark-theme.layui-header {
	border-bottom: none;
	background-color: #28333E;
	color: whitesmoke;
}

.dark-theme.layui-header li>a{
	color: whitesmoke!important;
}

.dark-theme.layui-header .layui-logo {
	box-shadow: none;
	border: none;
}

/** 顶部主题 (白) */
.light-theme.layui-header .layui-logo {
	background-color: white;
	border: none;
	box-shadow: none;
}

/** 主题面板 */
.pearone-color .set-text {
	height: 42px;
	line-height: 42px;
}

.pearone-color .color-title {
	padding: 15px 0 0px 20px;
	margin-bottom: 4px;
}

.pearone-color .color-content {
	padding: 15px 10px 0 20px;
}

.pearone-color .color-content ul {
	list-style: none;
	padding: 0px;
}

.pearone-color .color-content ul li {
	position: relative;
	display: inline-block;
	vertical-align: top;
	width: 70px;
	height: 50px;
	margin: 0 20px 20px 0;
	padding: 2px 2px 2px 2px;
	background-color: #f2f2f2;
	cursor: pointer;
	font-size: 12px;
	color: #666;
}

.pearone-color .color-content li.layui-this:after,
.pearone-color .color-content li:hover:after {
	width: 100%;
	height: 100%;
	padding: 4px;
	top: -5px;
	left: -5px;
	border: #5FB878 2px solid;
	opacity: 1;
	border-radius: 4px;
}

.pearone-color .color-content li:after {
	content: '';
	position: absolute;
	z-index: 20;
	top: 50%;
	left: 50%;
	width: 1px;
	height: 0;
	border: 2px solid #F2F2F2;
	transition: all .3s;
	-webkit-transition: all .3s;
	opacity: 0;
}
@media screen and (max-width: 450px) {
  .pearone-color .layui-form-item .layui-input-inline {
      float: left !important;
      width: 190px !important;
      margin: 0 10px 0 0!important;
  }
}
.select-color {
	margin-bottom: 30px;
}

.select-color .select-color-title {
	padding: 15px 0 0px 20px;
	margin-bottom: 4px;
}

.select-color .select-color-content {
	padding: 20px 0 0px 0px;
	margin-bottom: 4px;
}

.select-color .select-color-content .select-color-item {
	background-color: gray;
	width: 30px;
	height: 30px;
	border-radius: 3px;
	float: left;
	margin-left: 20px;
	color: white;
	font-size: 18px;
	text-align: center;
	box-shadow: 0 1px 2px 0 rgba(0, 0, 0, .15);
	line-height: 30px;
}

.message .layui-tab-title li:not(:last-child) {
	border-right: 1px solid #eee;
}

/* 搜索面板 */
.menu-search-content .layui-input {
	padding-left: 30px;
}

.menu-search-content {
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
}

.menu-search-input-wrapper {
	width: 100%;
	padding: 15px 15px;
}

.menu-search-no-data {
	display: flex;
	justify-content: center;
	width: 100%;
	height: 122px;
	align-items: center;
}

.menu-search-list {
	width: 100%;
	padding: 5px 15px;
}

.menu-search-list li {
	position: relative;
	display: flex;
	justify-content: space-between;
	align-items: center;
	flex-wrap: nowrap;
	height: 50px;
	margin-bottom: 8px;
	padding: 0px 10px;
	color: currentColor;
	font-size: 14px;
	border-radius: 4px;
	box-shadow: 0 1px 3px #d4d9e1;
	cursor: pointer;
	background-color: #fff;
}

.menu-search-list li:hover {
	background-color: #5FB878;
	color: white;
}

.menu-search-list li.this {
	background-color: #5FB878;
	color: white;
}

/* 搜索面板结束 */