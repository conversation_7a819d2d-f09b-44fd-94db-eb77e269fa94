/* ===== 四方支付系统 - 暗黑科技风格菜单专用样式 ===== */

/* 强制覆盖layui默认样式 */
.layui-nav-tree {
	background: transparent !important;
	width: 100% !important;
}

.layui-nav-tree .layui-nav-item {
	margin: 0 !important;
	border-bottom: none !important;
	position: relative !important;
}

/* 主菜单项样式 */
.layui-nav-tree .layui-nav-item > a {
	color: rgba(255, 255, 255, 0.85) !important;
	padding: 16px 20px !important;
	margin: 3px 12px !important;
	border-radius: 10px !important;
	transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
	font-weight: 500 !important;
	font-size: 14px !important;
	position: relative !important;
	border: 1px solid transparent !important;
	background: transparent !important;
	display: flex !important;
	align-items: center !important;
	min-height: 50px !important;
	text-decoration: none !important;
	overflow: hidden !important;
}

/* 菜单图标 */
.layui-nav-tree .layui-nav-item > a .layui-icon {
	margin-right: 14px !important;
	font-size: 17px !important;
	opacity: 0.9 !important;
	transition: all 0.3s ease !important;
	width: 20px !important;
	text-align: center !important;
}

/* 菜单文字 */
.layui-nav-tree .layui-nav-item > a span {
	flex: 1 !important;
	transition: all 0.3s ease !important;
}

/* 左侧装饰线 */
.layui-nav-tree .layui-nav-item > a::before {
	content: '' !important;
	position: absolute !important;
	left: 0 !important;
	top: 50% !important;
	transform: translateY(-50%) !important;
	width: 4px !important;
	height: 0 !important;
	background: linear-gradient(180deg, #00d4ff 0%, #4ecdc4 100%) !important;
	border-radius: 0 3px 3px 0 !important;
	transition: height 0.4s ease !important;
}

/* 悬停效果 */
.layui-nav-tree .layui-nav-item > a:hover {
	background: linear-gradient(135deg, rgba(0, 212, 255, 0.12) 0%, rgba(0, 212, 255, 0.06) 100%) !important;
	color: #00d4ff !important;
	border-color: rgba(0, 212, 255, 0.4) !important;
	box-shadow: 
		0 0 25px rgba(0, 212, 255, 0.15),
		inset 0 0 25px rgba(0, 212, 255, 0.08) !important;
	transform: translateX(6px) !important;
}

.layui-nav-tree .layui-nav-item > a:hover::before {
	height: 70% !important;
}

.layui-nav-tree .layui-nav-item > a:hover .layui-icon {
	color: #00d4ff !important;
	opacity: 1 !important;
	text-shadow: 0 0 12px rgba(0, 212, 255, 0.6) !important;
	transform: scale(1.1) !important;
}

/* 选中状态 */
.layui-nav-tree .layui-this > a,
.layui-nav-tree .layui-nav-itemed > a {
	background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%) !important;
	color: #0a0a0a !important;
	border-color: #00d4ff !important;
	box-shadow: 
		0 0 30px rgba(0, 212, 255, 0.4),
		inset 0 1px 0 rgba(255, 255, 255, 0.2),
		inset 0 -1px 0 rgba(0, 0, 0, 0.1) !important;
	font-weight: 600 !important;
	transform: translateX(8px) !important;
}

.layui-nav-tree .layui-this > a::before,
.layui-nav-tree .layui-nav-itemed > a::before {
	height: 85% !important;
	background: linear-gradient(180deg, #ffffff 0%, #e0e0e0 100%) !important;
	width: 5px !important;
}

.layui-nav-tree .layui-this > a .layui-icon,
.layui-nav-tree .layui-nav-itemed > a .layui-icon {
	color: #0a0a0a !important;
	opacity: 1 !important;
	text-shadow: none !important;
	transform: scale(1.1) !important;
}

/* 子菜单容器 */
.layui-nav-tree .layui-nav-child {
	background: rgba(8, 8, 8, 0.95) !important;
	border-radius: 0 0 10px 10px !important;
	margin: 0 12px 8px 12px !important;
	border: 1px solid rgba(0, 212, 255, 0.25) !important;
	border-top: none !important;
	backdrop-filter: blur(15px) !important;
	box-shadow: 
		inset 0 0 25px rgba(0, 212, 255, 0.08),
		0 8px 25px rgba(0, 0, 0, 0.3) !important;
	padding: 8px 0 !important;
}

/* 子菜单项 */
.layui-nav-tree .layui-nav-child a {
	color: rgba(255, 255, 255, 0.7) !important;
	padding: 12px 20px 12px 50px !important;
	margin: 2px 8px !important;
	font-size: 13px !important;
	border: none !important;
	border-radius: 8px !important;
	min-height: 42px !important;
	transition: all 0.3s ease !important;
	position: relative !important;
	display: flex !important;
	align-items: center !important;
}

.layui-nav-tree .layui-nav-child a::before {
	content: '•' !important;
	position: absolute !important;
	left: 35px !important;
	color: rgba(0, 212, 255, 0.6) !important;
	font-size: 16px !important;
	transition: all 0.3s ease !important;
}

.layui-nav-tree .layui-nav-child a:hover {
	color: #00d4ff !important;
	background: rgba(0, 212, 255, 0.1) !important;
	transform: translateX(8px) !important;
	border-color: transparent !important;
	box-shadow: 0 0 20px rgba(0, 212, 255, 0.2) !important;
}

.layui-nav-tree .layui-nav-child a:hover::before {
	color: #00d4ff !important;
	text-shadow: 0 0 8px rgba(0, 212, 255, 0.8) !important;
	transform: scale(1.2) !important;
}

.layui-nav-tree .layui-nav-child .layui-this a {
	background: rgba(0, 212, 255, 0.2) !important;
	color: #00d4ff !important;
	border-left: 4px solid #00d4ff !important;
	font-weight: 600 !important;
	padding-left: 46px !important;
}

.layui-nav-tree .layui-nav-child .layui-this a::before {
	color: #00d4ff !important;
	text-shadow: 0 0 10px rgba(0, 212, 255, 1) !important;
	transform: scale(1.3) !important;
}

/* 展开箭头 */
.layui-nav-tree .layui-nav-more {
	border-color: rgba(255, 255, 255, 0.5) transparent transparent !important;
	transition: all 0.3s ease !important;
	right: 20px !important;
}

.layui-nav-tree .layui-nav-mored {
	border-color: transparent transparent rgba(255, 255, 255, 0.5) !important;
	transform: rotate(180deg) !important;
}

.layui-nav-tree .layui-nav-item:hover .layui-nav-more {
	border-top-color: #00d4ff !important;
}

.layui-nav-tree .layui-nav-item:hover .layui-nav-mored {
	border-bottom-color: #00d4ff !important;
}

.layui-nav-tree .layui-this .layui-nav-more,
.layui-nav-tree .layui-nav-itemed .layui-nav-more {
	border-top-color: #0a0a0a !important;
}

.layui-nav-tree .layui-this .layui-nav-mored,
.layui-nav-tree .layui-nav-itemed .layui-nav-mored {
	border-bottom-color: #0a0a0a !important;
}

/* 菜单分组标题 */
.nav-group-title {
	color: rgba(255, 255, 255, 0.4) !important;
	font-size: 11px !important;
	font-weight: 700 !important;
	text-transform: uppercase !important;
	letter-spacing: 2px !important;
	padding: 25px 20px 10px 20px !important;
	margin: 0 !important;
	border-bottom: 1px solid rgba(0, 212, 255, 0.15) !important;
	background: transparent !important;
	position: relative !important;
}

.nav-group-title::after {
	content: '' !important;
	position: absolute !important;
	bottom: -1px !important;
	left: 20px !important;
	width: 30px !important;
	height: 2px !important;
	background: linear-gradient(90deg, #00d4ff, #4ecdc4) !important;
}

/* 响应式适配 */
@media (max-width: 768px) {
	.layui-nav-tree .layui-nav-item > a {
		padding: 14px 16px !important;
		margin: 2px 8px !important;
	}
	
	.layui-nav-tree .layui-nav-child a {
		padding: 10px 16px 10px 40px !important;
	}
}

/* 收缩状态样式 */
.pear-mini .layui-nav-tree .layui-nav-item > a {
	justify-content: center !important;
	padding: 16px 12px !important;
	margin: 3px 8px !important;
}

.pear-mini .layui-nav-tree .layui-nav-item > a .layui-icon {
	margin-right: 0 !important;
	font-size: 20px !important;
}

.pear-mini .layui-nav-tree .layui-nav-item > a span {
	display: none !important;
}

.pear-mini .layui-nav-tree .layui-nav-more {
	display: none !important;
}

.pear-mini .layui-nav-tree .layui-nav-child {
	display: none !important;
}
