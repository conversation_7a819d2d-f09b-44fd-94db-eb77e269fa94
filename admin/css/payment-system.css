/* ===== 四方支付系统 - 全局样式 ===== */

:root {
	/* 主要配色方案 */
	--primary-color: #1a73e8;
	--primary-hover: #1557b0;
	--primary-light: #e8f0fe;
	--success-color: #34a853;
	--success-light: #e6f4ea;
	--warning-color: #fbbc04;
	--warning-light: #fef7e0;
	--error-color: #ea4335;
	--error-light: #fce8e6;
	--info-color: #4285f4;
	--info-light: #e8f0fe;
	
	/* 中性色 */
	--background-color: #f8f9fa;
	--surface-color: #ffffff;
	--text-primary: #202124;
	--text-secondary: #5f6368;
	--text-disabled: #9aa0a6;
	--border-color: #dadce0;
	--border-light: #e8eaed;
	--divider-color: #f1f3f4;
	
	/* 阴影 */
	--shadow-light: 0 1px 3px rgba(60,64,67,0.3);
	--shadow-medium: 0 4px 6px rgba(60,64,67,0.15);
	--shadow-heavy: 0 8px 25px rgba(60,64,67,0.12);
	--shadow-focus: 0 0 0 3px rgba(26, 115, 232, 0.1);
	
	/* 圆角 */
	--border-radius: 8px;
	--border-radius-large: 12px;
	--border-radius-small: 4px;
	
	/* 间距 */
	--spacing-xs: 4px;
	--spacing-sm: 8px;
	--spacing-md: 16px;
	--spacing-lg: 24px;
	--spacing-xl: 32px;
	--spacing-xxl: 48px;
	
	/* 字体 */
	--font-family: 'Google Sans', 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
	--font-size-xs: 12px;
	--font-size-sm: 13px;
	--font-size-base: 14px;
	--font-size-lg: 16px;
	--font-size-xl: 18px;
	--font-size-xxl: 20px;
	--font-size-title: 24px;
	--font-size-display: 32px;
	
	/* 动画 */
	--transition-fast: 0.15s ease;
	--transition-base: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	--transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 全局重置 */
* {
	box-sizing: border-box;
}

body {
	font-family: var(--font-family);
	color: var(--text-primary);
	background-color: var(--background-color);
	line-height: 1.5;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

/* 卡片组件 */
.payment-card {
	background: var(--surface-color);
	border-radius: var(--border-radius-large);
	box-shadow: var(--shadow-medium);
	border: 1px solid var(--border-light);
	transition: var(--transition-base);
	overflow: hidden;
}

.payment-card:hover {
	transform: translateY(-2px);
	box-shadow: var(--shadow-heavy);
}

.payment-card-header {
	padding: var(--spacing-lg);
	border-bottom: 1px solid var(--border-light);
	background: linear-gradient(135deg, var(--surface-color) 0%, var(--background-color) 100%);
	font-weight: 600;
	font-size: var(--font-size-lg);
	color: var(--text-primary);
}

.payment-card-body {
	padding: var(--spacing-lg);
}

/* 按钮组件 */
.payment-btn {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	padding: var(--spacing-sm) var(--spacing-lg);
	border-radius: var(--border-radius);
	font-weight: 500;
	font-size: var(--font-size-base);
	text-decoration: none;
	border: none;
	cursor: pointer;
	transition: var(--transition-base);
	min-height: 40px;
	gap: var(--spacing-sm);
}

.payment-btn-primary {
	background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
	color: white;
}

.payment-btn-primary:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 12px rgba(26, 115, 232, 0.3);
}

.payment-btn-success {
	background: linear-gradient(135deg, var(--success-color) 0%, #2d7738 100%);
	color: white;
}

.payment-btn-warning {
	background: linear-gradient(135deg, var(--warning-color) 0%, #e8a317 100%);
	color: var(--text-primary);
}

.payment-btn-error {
	background: linear-gradient(135deg, var(--error-color) 0%, #d33b2c 100%);
	color: white;
}

.payment-btn-outline {
	background: transparent;
	border: 2px solid var(--primary-color);
	color: var(--primary-color);
}

.payment-btn-outline:hover {
	background: var(--primary-color);
	color: white;
}

/* 表单组件 */
.payment-input {
	width: 100%;
	padding: var(--spacing-sm) var(--spacing-md);
	border: 2px solid var(--border-color);
	border-radius: var(--border-radius);
	font-size: var(--font-size-base);
	transition: var(--transition-base);
	background: var(--surface-color);
}

.payment-input:focus {
	outline: none;
	border-color: var(--primary-color);
	box-shadow: var(--shadow-focus);
}

.payment-input::placeholder {
	color: var(--text-disabled);
}

/* 状态指示器 */
.payment-status {
	display: inline-flex;
	align-items: center;
	padding: var(--spacing-xs) var(--spacing-sm);
	border-radius: var(--border-radius-small);
	font-size: var(--font-size-xs);
	font-weight: 500;
	gap: var(--spacing-xs);
}

.payment-status-success {
	background: var(--success-light);
	color: var(--success-color);
}

.payment-status-warning {
	background: var(--warning-light);
	color: #b7791f;
}

.payment-status-error {
	background: var(--error-light);
	color: var(--error-color);
}

.payment-status-info {
	background: var(--info-light);
	color: var(--info-color);
}

/* 数据统计卡片 */
.payment-stat-card {
	background: var(--surface-color);
	border-radius: var(--border-radius-large);
	padding: var(--spacing-lg);
	box-shadow: var(--shadow-medium);
	border-left: 4px solid var(--primary-color);
	transition: var(--transition-base);
}

.payment-stat-card:hover {
	transform: translateY(-4px);
	box-shadow: var(--shadow-heavy);
}

.payment-stat-value {
	font-size: var(--font-size-display);
	font-weight: 700;
	color: var(--text-primary);
	margin-bottom: var(--spacing-xs);
}

.payment-stat-label {
	font-size: var(--font-size-sm);
	color: var(--text-secondary);
	font-weight: 500;
}

/* 表格样式 */
.payment-table {
	width: 100%;
	background: var(--surface-color);
	border-radius: var(--border-radius-large);
	overflow: hidden;
	box-shadow: var(--shadow-medium);
}

.payment-table th {
	background: var(--background-color);
	padding: var(--spacing-md);
	font-weight: 600;
	color: var(--text-primary);
	border-bottom: 1px solid var(--border-light);
}

.payment-table td {
	padding: var(--spacing-md);
	border-bottom: 1px solid var(--divider-color);
}

.payment-table tr:hover {
	background: var(--background-color);
}

/* 工具类 */
.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-success { color: var(--success-color); }
.text-warning { color: #b7791f; }
.text-error { color: var(--error-color); }
.text-info { color: var(--info-color); }

.bg-primary { background-color: var(--primary-color); }
.bg-success { background-color: var(--success-color); }
.bg-warning { background-color: var(--warning-color); }
.bg-error { background-color: var(--error-color); }
.bg-info { background-color: var(--info-color); }

.border-primary { border-color: var(--primary-color); }
.border-success { border-color: var(--success-color); }
.border-warning { border-color: var(--warning-color); }
.border-error { border-color: var(--error-color); }
.border-info { border-color: var(--info-color); }

.rounded { border-radius: var(--border-radius); }
.rounded-lg { border-radius: var(--border-radius-large); }
.rounded-sm { border-radius: var(--border-radius-small); }

.shadow-sm { box-shadow: var(--shadow-light); }
.shadow { box-shadow: var(--shadow-medium); }
.shadow-lg { box-shadow: var(--shadow-heavy); }

.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }

/* 动画 */
@keyframes fadeInUp {
	from {
		opacity: 0;
		transform: translateY(20px);
	}
	to {
		opacity: 1;
		transform: translateY(0);
	}
}

@keyframes slideInRight {
	from {
		opacity: 0;
		transform: translateX(20px);
	}
	to {
		opacity: 1;
		transform: translateX(0);
	}
}

@keyframes pulse {
	0%, 100% {
		transform: scale(1);
	}
	50% {
		transform: scale(1.05);
	}
}

.animate-fade-in-up {
	animation: fadeInUp 0.6s ease-out;
}

.animate-slide-in-right {
	animation: slideInRight 0.6s ease-out;
}

.animate-pulse {
	animation: pulse 2s infinite;
}
