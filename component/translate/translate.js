/*
 * 多语言翻译，作者：管雷鸣
 * 开源仓库： https://gitee.com/mail_osc/translate_layui
 */      
layui.define([], function (exports) {
	//下行是直接将https://gitee.com/mail_osc/translate 压缩后粘贴过来
	var translate={version:"3.1.5.20240318",useVersion:"v2",setUseVersion2:function(){translate.useVersion="v2",console.log("提示：自 v2.10 之后的版本默认就是使用V2版本（当前版本为:"+translate.version+"）， translate.setUseVersion2() 可以不用再加这一行了。当然加了也无所谓，只是加了跟不加是完全一样的。")},translate:null,includedLanguages:"zh-CN,zh-TW,en",resourcesUrl:"//res.zvo.cn/translate",selectLanguageTag:{documentId:"translate",show:!0,languages:"",alreadyRender:!1,selectOnChange:function(e){var t=e.target.value;translate.changeLanguage(t)},refreshRender:function(){let e=document.getElementById(translate.selectLanguageTag.documentId+"SelectLanguage");e&&e.parentNode.removeChild(e),translate.selectLanguageTag.alreadyRender=!1,translate.selectLanguageTag.render()},render:function(){if(!translate.selectLanguageTag.alreadyRender&&(translate.selectLanguageTag.alreadyRender=!0,translate.selectLanguageTag.show)){if(null==document.getElementById(translate.selectLanguageTag.documentId)){var e=document.getElementsByTagName("body")[0],t=document.createElement("div");t.id=translate.selectLanguageTag.documentId,e.appendChild(t)}else if(null!=document.getElementById(translate.selectLanguageTag.documentId+"SelectLanguage"))return;translate.request.post(translate.request.api.language,{},function(e){if(0!=e.result){var t=function(e){translate.selectLanguageTag.selectOnChange(e)},a=document.createElement("select");a.id=translate.selectLanguageTag.documentId+"SelectLanguage",a.className=translate.selectLanguageTag.documentId+"SelectLanguage";for(var n=0;n<e.list.length;n++){var r=document.createElement("option");if(r.setAttribute("value",e.list[n].id),translate.selectLanguageTag.languages.length>0)if((","+translate.selectLanguageTag.languages+",").toLowerCase().indexOf(","+e.list[n].id.toLowerCase()+",")<0)continue;null!=translate.to&&void 0!==translate.to&&translate.to.length>0?translate.to==e.list[n].id&&r.setAttribute("selected","selected"):e.list[n].id==translate.language.getLocal()&&r.setAttribute("selected","selected"),r.appendChild(document.createTextNode(e.list[n].name)),a.appendChild(r)}window.addEventListener?a.addEventListener("change",t,!1):a.attachEvent("onchange",t),document.getElementById(translate.selectLanguageTag.documentId).appendChild(a)}else console.log("load language list error : "+e.info)})}}},localLanguage:"zh-CN",googleTranslateElementInit:function(){var e="";null!=document.getElementById("translate")&&(e="translate"),translate.translate=new google.translate.TranslateElement({pageLanguage:"zh-CN",includedLanguages:translate.selectLanguageTag.languages,layout:0},e)},execute_v1:function(){console.log("=====ERROR======"),console.log("The v1 version has been discontinued since 2022. Please use the latest V3 version and refer to: http://translate.zvo.cn/41162.html")},setCookie:function(e,t){var a=e+"="+escape(t);document.cookie=a},getCookie:function(e){for(var t=document.cookie.split("; "),a=0;a<t.length;a++){var n=t[a].split("=");if(n[0]==e)return unescape(n[1])}return""},currentLanguage:function(){var e=translate.getCookie("googtrans");return e.length>0?e.substr(e.lastIndexOf("/")+1,e.length-1):translate.localLanguage},changeLanguage:function(e){if(",en,de,hi,lt,hr,lv,ht,hu,zh-CN,hy,uk,mg,id,ur,mk,ml,mn,af,mr,uz,ms,el,mt,is,it,my,es,et,eu,ar,pt-PT,ja,ne,az,fa,ro,nl,en-GB,no,be,fi,ru,bg,fr,bs,sd,se,si,sk,sl,ga,sn,so,gd,ca,sq,sr,kk,st,km,kn,sv,ko,sw,gl,zh-TW,pt-BR,co,ta,gu,ky,cs,pa,te,tg,th,la,cy,pl,da,tr,".indexOf(","+e+",")>-1){console.log("您使用的是v1版本的切换语种方式，v1已在2021年就以废弃，请更换为v2，参考文档： http://translate.zvo.cn/41549.html"),translate.check();var t="/"+translate.localLanguage+"/"+e,a=document.location.host.split(".");if(a.length>2){var n=a[a.length-2]+"."+a[a.length-1];document.cookie="googtrans=;expires="+new Date(1)+";domain="+n+";path=/",document.cookie="googtrans="+t+";domain="+n+";path=/"}return translate.setCookie("googtrans",""+t),void location.reload()}if(translate.useVersion="v2",null!=translate.to&&translate.to.length>0&&translate.to!=translate.language.getLocal())var r=!0;translate.to=e,translate.storage.set("to",e),r?location.reload():translate.execute()},check:function(){"file:"==window.location.protocol&&console.log("\r\n---WARNING----\r\ntranslate.js 主动翻译组件自检异常，当前协议是file协议，翻译组件要在正常的线上http、https协议下才能正常使用翻译功能\r\n------------")},to:"",autoDiscriminateLocalLanguage:!1,documents:[],inProgressNodes:[],ignore:{tag:["style","script","link","pre","code"],class:["ignore","translateSelectLanguage"],id:[],isIgnore:function(e){if(null==e||void 0===e)return!1;for(var t=e,a=100;a-- >0;){if(null==t||void 0===t)return!1;var n=translate.element.getNodeName(t).toLowerCase();if(n.length>0){if("body"==n||"html"==n||"#document"==n)return!1;if(translate.ignore.tag.indexOf(n)>-1)return!0}if(null!=t.className){var r=t.className;if(null==r||"string"!=typeof r)continue;r=r.trim().split(" ");for(var s=0;s<r.length;s++)if(null!=r[s]&&r[s].trim().length>0&&translate.ignore.class.indexOf(r[s])>-1)return!0}if(null!=t.id&&void 0!==t.id&&translate.ignore.id.indexOf(t.id)>-1)return!0;t=t.parentNode}return!1}},nomenclature:{data:new Array,old_Data:[],set:function(e){alert("请将 translate.nomenclature.set 更换为 append，具体使用可参考： https://github.com/xnx3/translate ")},append:function(e,t,a){void 0===translate.nomenclature.data[e]&&(translate.nomenclature.data[e]=new Array),void 0===translate.nomenclature.data[e][t]&&(translate.nomenclature.data[e][t]=new Array);for(var n=a.split("\n"),r=0;r<n.length;r++){var s=n[r].trim();if(!(s.length<1)){var l=s.split("=");if(2==l.length){var o=l[0].trim(),i=l[1].trim();0!=o.length&&0!=i.length&&(translate.nomenclature.data[e][t][o]=i)}}}translate.nomenclature.data[e][t]=translate.util.objSort(translate.nomenclature.data[e][t])},get:function(){return translate.nomenclature.data},dispose:function(e){if(null==e||0==e.length)return e;if(void 0===translate.nomenclature.data[translate.language.getLocal()]||void 0===translate.nomenclature.data[translate.language.getLocal()][translate.to])return e;for(var t in translate.nomenclature.data[translate.language.getLocal()][translate.to]){var a=translate.nomenclature.data[translate.language.getLocal()][translate.to][t];if("function"!=typeof a){var n=e.indexOf(t);if(n>-1)if("english"==translate.language.getLocal()){var r="";if(0==n);else if(r=e.substr(n-1,1),"english"==translate.language.getCharLanguage(r))continue;var s="";if(n+t.length==e.length);else if(s=e.substr(n+t.length,1),"english"==translate.language.getCharLanguage(s))continue;e=e.replace(new RegExp(r+t+s,"g"),r+a+s)}else e=e.replace(new RegExp(t,"g"),a)}}return e}},office:{export:function(){if(translate.language.getLocal()!=translate.language.getCurrent()){var e="";for(var t in translate.nodeQueue){translate.nodeQueue[t];for(var a in translate.nodeQueue[t].list)if(!("string"!=typeof a||a.length<1))for(var n in translate.nodeQueue[t].list[a])e=e+"\n"+translate.nodeQueue[t].list[a][n].original+"="+translate.storage.get("hash_"+translate.language.getCurrent()+"_"+n)}e.length>0?(e="translate.office.append('"+translate.language.getCurrent()+"',`"+e+"\n`);",translate.util.loadMsgJs(),msg.popups({text:'<textarea id="msgPopupsTextarea" style="width:100%; height:100%; color: black; padding: 8px;">loaing...</textarea>',width:"750px",height:"600px",padding:"1px"}),document.getElementById("msgPopupsTextarea").value=e):msg.alert("无有效内容！")}else alert("本地语种跟要翻译的语种一致，无需导出")},showPanel:function(){let e=document.createElement("div");e.setAttribute("id","translate_export"),e.setAttribute("class","ignore");let t=document.createElement("button");t.onclick=function(){translate.office.export()},t.innerHTML="导出配置信息",t.setAttribute("style","margin-left: 72px; margin-top: 30px; margin-bottom: 20px; font-size: 25px; background-color: blue; padding: 15px; padding-top: 3px; padding-bottom: 3px; border-radius: 3px;"),e.appendChild(t);let a=document.createElement("div");a.innerHTML='1. 首先将当前语种切换为你要翻译的语种<br/>2. 点击导出按钮，将翻译的配置信息导出<br/>3. 将导出的配置信息粘贴到代码中，即可完成<br/><a href="asd" target="_black" style="color: aliceblue;">点此进行查阅详细使用说明</a>',a.setAttribute("style","font-size: 14px; padding: 12px;"),e.appendChild(a),e.setAttribute("style","background-color: black; color: #fff; width: 320px; height: 206px; position: fixed; bottom: 50px; right: 50px;"),document.body.appendChild(e),translate.util.loadMsgJs()},append:function(e,t){for(var a=t.split("\n"),n=0;n<a.length;n++){var r=a[n].trim();if(!(r.length<1)){var s=r.split("=");if(2==s.length){var l=s[0],o=s[1];0!=l.length&&0!=o.length&&translate.storage.set("hash_"+e+"_"+translate.util.hash(l),o)}}}}},setAutoDiscriminateLocalLanguage:function(){translate.autoDiscriminateLocalLanguage=!0},nodeQueue:{},setDocuments:function(e){null!=e&&void 0!==e&&(void 0===e.length?translate.documents[0]=e:translate.documents=e,translate.nodeQueue={})},getDocuments:function(){return null!=translate.documents&&void 0!==translate.documents&&translate.documents.length>0?translate.documents:document.all},listener:{isStart:!1,start:function(){translate.temp_linstenerStartInterval=setInterval(function(){"complete"==document.readyState&&(clearInterval(translate.temp_linstenerStartInterval),translate.listener.addListener())},300)},addListener:function(){translate.listener.isStart=!0,translate.listener.config={attributes:!0,childList:!0,subtree:!0,characterData:!0,attributeOldValue:!0,characterDataOldValue:!0},translate.listener.callback=function(e,t){var a=[];for(let t of e){let e=[];"childList"===t.type?t.addedNodes.length>0?e=t.addedNodes:t.removedNodes.length:"attributes"===t.type||"characterData"===t.type&&(e=[t.target]);for(let t of e){for(var n=!1,r=0;r<a.length;r++)if(a[r].isSameNode(t)){n=!0;break}if(n)break;a.push.apply(a,[t])}}if(a.length>0){var s=[];for(let e of a){for(var l=!1,o=0;o<translate.inProgressNodes.length;o++)if(translate.inProgressNodes[o].node.isSameNode(e)){l=!0;break}l||s.push(e)}if(s.length<1)return;setTimeout(function(){translate.execute(s)},10)}},translate.listener.observer=new MutationObserver(translate.listener.callback);for(var e=translate.getDocuments(),t=0;t<e.length;t++){var a=e[t];null!=a&&translate.listener.observer.observe(a,translate.listener.config)}},renderTaskFinish:function(e){}},renderTask:class{constructor(){this.taskQueue=[],this.nodes=[]}add(e,t,a,n){var r=translate.element.nodeAnalyse.get(e,n),s=translate.util.hash(r.text);void 0===this.nodes[s]&&(this.nodes[s]=new Array),this.nodes[s].push(e);var l=this.taskQueue[s];null!=l&&void 0!==l||(l=new Array);var o=new Array;" "==t.substr(0,1)&&" "!=a.substr(0,1)&&(a=" "+a)," "===t.substr(t.length-1,1)&&" "!=a.substr(0,1)&&(a+=" "),o.originalText=t,o.resultText=a,o.attribute=n,l.push(o),this.taskQueue[s]=l}execute(){for(var e in this.taskQueue){"function"!=typeof(t=this.taskQueue[e])&&(t.sort((e,t)=>t.originalText.length-e.originalText.length),this.taskQueue[e]=t)}for(var e in this.nodes)for(var t=this.taskQueue[e],a=0;a<this.nodes[e].length;a++)for(var n=0;n<t.length;n++){var r=t[n];if("function"!=typeof t){var s=this.nodes[e][n];setTimeout(function(e){for(var t=0;t<translate.inProgressNodes.length;t++)if(translate.inProgressNodes[t].node.isSameNode(e)){translate.inProgressNodes[t].number=translate.inProgressNodes[t].number-1,translate.inProgressNodes[t].number<1&&translate.inProgressNodes.splice(t,1);break}},50,s),translate.element.nodeAnalyse.set(this.nodes[e][n],r.originalText,r.resultText,r.attribute)}}if(void 0!==this.taskQueue&&Object.keys(this.taskQueue).length>0){var l=this;setTimeout(function(){for(var e in l.nodes)for(var t in l.nodes[e]){var a=translate.element.nodeAnalyse.get(l.nodes[e][0]),n=nodeuuid.uuid(a.node);0!=n.length&&(translate.nodeHistory[n]={},translate.nodeHistory[n].node=a.node,translate.nodeHistory[n].translateText=a.text)}translate.listener.renderTaskFinish(l)},50)}}},execute:function(e){void 0!==e&&(translate.useVersion="v2"),"v1"==translate.useVersion&&(console.log("提示：https://github.com/xnx3/translate 在 v2.5 版本之后，由于谷歌翻译调整，免费翻译通道不再支持，所以v1版本的翻译接口不再被支持，v1全线下架。考虑到v1已不能使用，当前已自动切换到v2版本。如果您使用中发现什么异常，请针对v2版本进行适配。"),translate.useVersion="v2");try{translate.init()}catch(e){}var t=translate.util.uuid();if(null==translate.to||""==translate.to){var a=translate.storage.get("to");null!=a&&void 0!==a&&a.length>0&&(translate.to=a)}try{translate.selectLanguageTag.render()}catch(e){console.log(e)}if(null!=translate.to&&void 0!==translate.to&&0!=translate.to.length){if(translate.to!=translate.language.getLocal()){var n;if(translate.images.execute(),void 0!==e){if(null==e)return void console.log("translate.execute(...) 中传入的要翻译的目标区域不存在。");void 0===e.length?(n=new Array)[0]=e:n=e}else n=translate.getDocuments();for(var r=0;r<n.length&r<20;r++){var s=n[r];translate.element.whileNodes(t,s)}if(translate.language.translateLanguagesRange.length>0)for(var l in translate.nodeQueue[t].list)translate.language.translateLanguagesRange.indexOf(l)<0&&delete translate.nodeQueue[t].list[l];for(var l in translate.nodeQueue[t].list){for(var o in translate.nodeQueue[t].list[l])if("function"!=typeof translate.nodeQueue[t].list[l][o]){for(var i=translate.nodeQueue[t].list[l][o].nodes.length-1;i>-1;i--){var u=translate.element.nodeAnalyse.get(translate.nodeQueue[t].list[l][o].nodes[i].node),d=nodeuuid.uuid(u.node);void 0!==translate.nodeHistory[d]&&translate.nodeHistory[d].translateText==u.text&&translate.nodeQueue[t].list[l][o].nodes.splice(i,1)}0==translate.nodeQueue[t].list[l][o].nodes.length&&delete translate.nodeQueue[t].list[l][o]}0==Object.keys(translate.nodeQueue[t].list[l]).length&&delete translate.nodeQueue[t].list[l]}var g={},c={},f={},h=[];for(var l in translate.nodeQueue[t].list){if(null==l||void 0===l||0==l.length||"undefined"==l)continue;g[l]=[],c[l]=[];let e=new translate.renderTask;for(var o in f[l]=[],translate.nodeQueue[t].list[l])if("function"!=typeof translate.nodeQueue[t].list[l][o]){var v=translate.nodeQueue[t].list[l][o].original,p=translate.nodeQueue[t].list[l][o].translateText,m=v==p?o:translate.util.hash(p);translate.nodeQueue[t].list[l][o].cacheHash=m;var x=translate.storage.get("hash_"+translate.to+"_"+m);if(null!=x&&x.length>0)for(var y=0;y<translate.nodeQueue[t].list[l][o].nodes.length;y++){for(var T=translate.nodeQueue[t].list[l][o].nodes[y].node,b=!1,w=0;w<translate.inProgressNodes.length;w++)translate.inProgressNodes[w].node.isSameNode(T)&&(translate.inProgressNodes[w].number++,b=!0);b||translate.inProgressNodes.push({node:T,number:1});var I=translate.nodeQueue[t].list[l][o].nodes[y].beforeText+x+translate.nodeQueue[t].list[l][o].nodes[y].afterText;e.add(translate.nodeQueue[t].list[l][o].nodes[y].node,v,I,translate.nodeQueue[t].list[l][o].nodes[y].attribute);var N=-1;for(r=0;r<f[l].length;r++)if(translate.nodeQueue[t].list[l][o].nodes[y].node.isSameNode(f[l][r].node)){N=r;break}var Q=-1;for(r=0;r<h.length;r++)if(translate.nodeQueue[t].list[l][o].nodes[y].node.isSameNode(h[r].node)){Q=r;break}-1==N&&(N=f[l].length,f[l][N]={},f[l][N].node=translate.nodeQueue[t].list[l][o].nodes[y].node,f[l][N].array=[]),-1==Q&&(h[Q=h.length]={},h[Q].node=translate.nodeQueue[t].list[l][o].nodes[y].node,h[Q].array=[]);var C=f[l][N].array.length;f[l][N].array[C]=I;var k=h[Q].array.length;h[Q].array[k]=I}else g[l].push(p),c[l].push(o)}e.execute()}for(var l in f){var L=Object.keys(translate.nodeQueue[t].list[l]),q=L.length;for(r=0;r<f[l].length;r++){for(var E=0;E<h.length;E++)if(f[l][r].node.isSameNode(h[E].node)){f[l][r].array=h[E].array;break}f[l][r].array.sort(function(e,t){return t.length-e.length});for(var O=translate.element.nodeAnalyse.get(f[l][r].node),A=O.text,S=0;S<f[l][r].array.length;S++)f[l][r].array[S]<1||(A=A.replace(new RegExp(translate.util.regExp.pattern(f[l][r].array[S]),"g"),translate.util.regExp.resultText("\n")));for(var R=A.split("\n"),D=0;D<R.length;D++)R[D]<1||translate.addNodeToQueue(t,O.node,R[D])}var _=Object.keys(translate.nodeQueue[t].list[l]);if(q-_.length!=0)for(var F=0;F<_.length;F++)if(twoHash=_[F],-1==L.indexOf(twoHash)){var j=translate.nodeQueue[t].list[l][twoHash];m=j.original==j.translateText?twoHash:translate.util.hash(j.translateText);translate.nodeQueue[t].list[l][twoHash].cacheHash=m,g[l].push(j.translateText),c[l].push(twoHash)}}var H=[];for(var l in translate.nodeQueue[t].list)void 0!==g[l]&&(g[l].length<1||H.push(l));if(0!=H.length){for(var l in c)if(void 0!==c[l]&&!(c[l].length<1))for(var P=0;P<c[l].length;P++)for(var V=c[l][P],M=0;M<translate.nodeQueue[t].list[l][V].nodes.length;M++){for(T=translate.nodeQueue[t].list[l][V].nodes[M].node,b=!1,w=0;w<translate.inProgressNodes.length;w++)translate.inProgressNodes[w].node.isSameNode(T)&&(translate.inProgressNodes[w].number++,b=!0);b||translate.inProgressNodes.push({node:T,number:1})}for(var z in H){l=H[z];if(void 0===g[l]||g[l].length<1)return;var B=translate.request.api.translate,$={from:l,to:translate.to,text:encodeURIComponent(JSON.stringify(g[l]))};translate.request.post(B,$,function(e){if(0==e.result)return console.log("=======ERROR START======="),console.log(g[e.from]),console.log("response : "+e.info),void console.log("=======ERROR END  =======");let a=new translate.renderTask;for(var n=0;n<c[e.from].length;n++){var r=e.from,s=e.text[n];if(null!=s){s.toLowerCase().indexOf(g[e.from][n].toLowerCase())>-1&&(s=g[e.from][n]);var l=c[e.from][n],o=translate.nodeQueue[t].list[r][l].cacheHash,i="";try{i=translate.nodeQueue[t].list[r][l].original}catch(e){console.log("uuid:"+t+", originalWord:"+i+", lang:"+r+", hash:"+l+", text:"+s+", queue:"+translate.nodeQueue[t]),console.log(e);continue}for(var u=0;u<translate.nodeQueue[t].list[r][l].nodes.length;u++)a.add(translate.nodeQueue[t].list[r][l].nodes[u].node,i,translate.nodeQueue[t].list[r][l].nodes[u].beforeText+s+translate.nodeQueue[t].list[r][l].nodes[u].afterText,translate.nodeQueue[t].list[r][l].nodes[u].attribute);translate.storage.set("hash_"+e.to+"_"+o,s)}}a.execute()})}}}}else translate.autoDiscriminateLocalLanguage&&translate.executeByLocalLanguage()},nodeHistory:{},element:{nodeAnalyse:{get:function(e,t){return translate.element.nodeAnalyse.analyse(e,"","",t)},set:function(e,t,a,n){translate.element.nodeAnalyse.analyse(e,t,a,n)},analyse:function(e,t,a,n){var r=new Array;r.node=e,r.text="";var s=translate.element.getNodeName(e);if(null!=n&&"string"==typeof n&&n.length>0)return r.text=e[n],void 0!==t&&t.length>0&&(void 0!==e[n]?e[n]=e[n].replace(new RegExp(translate.util.regExp.pattern(t),"g"),translate.util.regExp.resultText(a)):console.log(e)),r;"#text"==s&&(void 0!==e.parentNode&&"TEXTAREA"==translate.element.getNodeName(e.parentNode)&&(s="TEXTAREA",e=e.parentNode));if("INPUT"==s||"TEXTAREA"==s){if(null==e.attributes||void 0===e.attributes)return r.text="",r;if("INPUT"==s&&void 0!==e.attributes.type&&null!=typeof e.attributes.type.nodeValue&&("button"==e.attributes.type.nodeValue.toLowerCase()||"submit"==e.attributes.type.nodeValue.toLowerCase())){var l=e.attributes.value;if(null!=l&&void 0!==l&&void 0!==l.nodeValue&&l.nodeValue.length>0)return void 0!==t&&t.length>0&&(l.nodeValue=l.nodeValue.replace(new RegExp(translate.util.regExp.pattern(t),"g"),translate.util.regExp.resultText(a))),r.text=l.nodeValue,r.node=l,r}return void 0!==e.attributes.placeholder?(void 0!==t&&t.length>0&&(e.attributes.placeholder.nodeValue=e.attributes.placeholder.nodeValue.replace(new RegExp(translate.util.regExp.pattern(t),"g"),translate.util.regExp.resultText(a))),r.text=e.attributes.placeholder.nodeValue,r.node=e.attributes.placeholder,r):(r.text="",r)}if("META"==s){if(void 0!==e.name&&null!=e.name){var o=e.name.toLowerCase();if("keywords"==o||"description"==o)return void 0!==t&&null!=t&&t.length>0&&(e.content=e.content.replace(new RegExp(translate.util.regExp.pattern(t),"g"),translate.util.regExp.resultText(a))),r.text=e.content,r}return r.text="",r}return"IMG"==s?void 0===e.alt||null==e.alt?(r.text="",r):(void 0!==t&&t.length>0&&(e.alt=e.alt.replace(new RegExp(translate.util.regExp.pattern(t),"g"),translate.util.regExp.resultText(a))),r.text=e.alt,r):(null==e.nodeValue||void 0===e.nodeValue?r.text="":0==e.nodeValue.trim().length?r.text="":(void 0!==t&&null!=t&&t.length>0&&(e.nodeValue=e.nodeValue.replace(new RegExp(translate.util.regExp.pattern(t),"g"),translate.util.regExp.resultText(a))),r.text=e.nodeValue),r)}},getNodeName:function(e){return null==e||void 0===e?"":null==e.nodeName||void 0===e.nodeName?"":e.nodeName},whileNodes:function(e,t){if(null!=t&&void 0!==t){void 0!==translate.nodeQueue[e]&&null!=translate.nodeQueue[e]||(translate.nodeQueue[e]=new Array,translate.nodeQueue[e].expireTime=Date.now()+12e4,translate.nodeQueue[e].list=new Array),"object"==typeof t&&"string"==typeof t.title&&t.title.length>0&&(translate.ignore.isIgnore(t)||translate.addNodeToQueue(e,t,t.title,"title"));var a=t.childNodes;if(a.length>0)for(var n=0;n<a.length;n++)translate.element.whileNodes(e,a[n]);else translate.element.findNode(e,t)}},findNode:function(e,t){if(null!=t&&void 0!==t&&null!=t.parentNode){var a=translate.element.getNodeName(t.parentNode);if(""!=a&&!(translate.ignore.tag.indexOf(a.toLowerCase())>-1||translate.ignore.isIgnore(t))){var n=translate.element.nodeAnalyse.get(t);n.text.length>0&&translate.addNodeToQueue(e,n.node,n.text)}}}},addNodeToQueue:function(e,t,a,n){if(null!=t&&null!=a&&0!=a.length&&"#comment"!=translate.element.getNodeName(t).toLowerCase()){translate.util.hash(a);if(translate.util.findTag(a)){if(null==t.parentNode)return;var r=translate.element.getNodeName(t.parentNode);if("SCRIPT"==r||"STYLE"==r)return}var s=translate.language.recognition(a);if(langs=s.languageArray,void 0!==langs[translate.to]&&delete langs[translate.to],translate.whole.isWhole(t)){l=s.languageName;translate.addNodeQueueItem(e,t,a,n,l,"","")}else for(var l in langs)for(var o=0;o<langs[l].list.length;o++)if(void 0!==langs[l].list[o]&&void 0!==langs[l].list[o].text){var i=langs[l].list[o].text,u=langs[l].list[o].beforeText,d=langs[l].list[o].afterText;translate.addNodeQueueItem(e,t,i,n,l,u,d)}}},addNodeQueueItem:function(e,t,a,n,r,s,l){null!=translate.nodeQueue[e].list[r]&&void 0!==translate.nodeQueue[e].list[r]||(translate.nodeQueue[e].list[r]=new Array);var o=translate.util.hash(a);null!=translate.nodeQueue[e].list[r][o]&&void 0!==translate.nodeQueue[e].list[r][o]||(translate.nodeQueue[e].list[r][o]=new Array,translate.nodeQueue[e].list[r][o].nodes=new Array,translate.nodeQueue[e].list[r][o].original=a,translate.nodeQueue[e].list[r][o].translateText=translate.nomenclature.dispose(a));var i=!1;if(void 0!==t.isSameNode)for(var u=0;u<translate.nodeQueue[e].list[r][o].nodes.length;u++)t.isSameNode(translate.nodeQueue[e].list[r][o].nodes[u].node)&&(i=!0);if(!i){var d=translate.nodeQueue[e].list[r][o].nodes.length;translate.nodeQueue[e].list[r][o].nodes[d]=new Array,translate.nodeQueue[e].list[r][o].nodes[d].node=t,translate.nodeQueue[e].list[r][o].nodes[d].attribute=n,translate.nodeQueue[e].list[r][o].nodes[d].beforeText=s,translate.nodeQueue[e].list[r][o].nodes[d].afterText=l}},whole:{class:[],tag:[],id:[],executeTip:function(){0==translate.whole.class.length&&0==translate.whole.tag.length&&0==translate.whole.id.length||console.log("您开启了 translate.whole 此次行为避开了浏览器端的文本语种自动识别，而是暴力的直接对某个元素的整个文本进行翻译，很可能会产生非常大的翻译量，请谨慎！有关每日翻译字符的说明，可参考： http://translate.zvo.cn/42557.html "),translate.whole.tag.indexOf("html")>-1&&console.log("自检发现您设置了 translate.whole.tag 其中有 html ，这个是不生效的，最大只允许设置到 body ")},isWhole:function(e){if(0==translate.whole.class.length&&0==translate.whole.tag.length&&0==translate.whole.id.length)return!1;if(null==e||void 0===e)return!1;for(var t=e,a=100;a-- >0;){if(null==t||void 0===t)return!1;var n=translate.element.getNodeName(t).toLowerCase();if(n.length>0){if("html"==n||"#document"==n)return!1;if(translate.whole.tag.indexOf(n)>-1)return!0}if(null!=t.className){var r=t.className;if(null==r||"string"!=typeof r)continue;r=r.trim().split(" ");for(var s=0;s<r.length;s++)if(null!=r[s]&&r[s].trim().length>0&&translate.whole.class.indexOf(r[s])>-1)return!0}if(null!=t.id&&void 0!==t.id&&translate.whole.id.indexOf(t.id)>-1)return!0;t=t.parentNode}return!1}},language:{local:"",translateLanguagesRange:[],setLocal:function(e){translate.useVersion="v2",translate.language.local=e},getLocal:function(){return(null==translate.language.local||translate.language.local.length<1)&&translate.language.autoRecognitionLocalLanguage(),translate.language.local},getCurrent:function(){var e=translate.storage.get("to");return null!=e&&void 0!==e&&e.length>0?e:translate.language.getLocal()},setDefaultTo:function(e){var t=translate.storage.get("to");null!=t&&void 0!==t&&t.length>0||(translate.storage.set("to",e),translate.to=e)},clearCacheLanguage:function(){translate.to="",translate.storage.set("to","")},setUrlParamControl:function(e){(void 0===e||e.length<1)&&(e="language");var t=translate.util.getUrlParam(e);void 0!==t&&""!=t&&"null"!=t&&"undefined"!=t&&(translate.storage.set("to",t),translate.to=t)},autoRecognitionLocalLanguage:function(){if(!(null!=translate.language.local&&translate.language.local.length>2)){var e=document.body.outerText;if(!(null==e||void 0===e||e.length<1)){e=e.replace(/\n|\t|\r/g,""),translate.language.local="chinese_simplified";var t=translate.language.recognition(e);return translate.language.local=t.languageName,translate.language.local}translate.language.local="chinese_simplified"}},get:function(e){for(var t=new Array,a=new Array,n=[],r=[],s=0;s<e.length;s++){var l=e.charAt(s),o=translate.language.getCharLanguage(l);""==o&&(o="unidentification");var i=translate.language.analyse(o,a,n,r,l);a=i.langStrs,void 0!==n.language&&(r.language=n.language,r.charstr=n.charstr,r.storage_language=n.storage_language),n.language=i.storage_language,n.charstr=l,n.storage_language=i.storage_language,t.push(o)}return void 0!==a.unidentification&&delete a.unidentification,void 0!==a.specialCharacter&&delete a.specialCharacter,void 0!==a.number&&delete a.number,a},recognition:function(e){var t=translate.language.get(e),a=[],n=[],r=0;for(var s in t){for(var l=0,o=0;o<t[s].length;o++)l+=t[s][o].text.length;r+=l,a[s]=l,n[s]=l}var i=[];for(var u in a)a[u]/r>.05&&(i[i.length]=u+"");i.length>1&&i.indexOf("english")>-1&&(a.english=0),i.indexOf("chinese_simplified")>-1&&i.indexOf("chinese_traditional")>-1&&(a.chinese_simplified=0);var d="",g=0;for(var u in a)a[u]>g&&(d=u,g=a[u]);var c={};for(var u in t)c[u]={},c[u].number=n[u],c[u].list=t[u];return{languageName:d,languageArray:c}},getCharLanguage:function(e){if(null==e||void 0===e)return"";if(this.italian(e))return"italian";if(this.english(e))return"english";if(this.specialCharacter(e))return"specialCharacter";if(this.number(e))return"number";var t=this.chinese(e);return"simplified"==t?"chinese_simplified":"traditional"==t?"chinese_traditional":this.japanese(e)?"japanese":this.korean(e)?"korean":""},analyse:function(e,t,a,n,r){void 0===t[e]&&(t[e]=new Array);var s=0;void 0===a.storage_language||(translate.language.connector(r)&&(e=a.storage_language),s=a.storage_language==e?t[e].length-1:t[e].length),void 0===t[e][s]&&(t[e][s]=new Array,t[e][s].beforeText="",t[e][s].afterText="",t[e][s].text=""),t[e][s].text=t[e][s].text+r,0==translate.language.wordBlankConnector(translate.language.getLocal())&&translate.language.wordBlankConnector(translate.to)&&null!=a.storage_language&&void 0!==a.storage_language&&a.storage_language.length>0&&"specialCharacter"!=a.storage_language&&(0==translate.language.wordBlankConnector(a.storage_language)&&translate.language.wordBlankConnector(e)?t[a.storage_language][t[a.storage_language].length-1].afterText=" ":"english"==a.storage_language&&"english"!=e&&(t[e][s].beforeText=" "));var l=new Array;return l.langStrs=t,l.storage_language=e,l},connector:function(e){return!!/.*[\u0020\u00A0\u202F\u205F\u3000]+.*$/.test(e)||(!!/.*[\u0030-\u0039]+.*$/.test(e)||(!!/.*[\u0021\u0022\u0023\u0024\u0025\u0026\u0027\u002C\u002D\u002E\u003A\u003B\u003F\u0040]+.*$/.test(e)||!!/.*[\u3002\uFF1F\uFF01\uFF0C\u3001\uFF1B\uFF1A\u300C\u300D\u300E\u300F\u2018\u2019\u201C\u201D\uFF08\uFF09\u3014\u3015\u3010\u3011\u2014\u2026\u2013\uFF0E\u300A\u300B\u3008\u3009\u00b7]+.*$/.test(e)))},wordBlankConnector:function(e){if(null==e||void 0===e)return!0;switch(e.trim().toLowerCase()){case"chinese_simplified":case"chinese_traditional":case"korean":case"japanese":return!1}return!0},chinese_traditional_dict:"皚藹礙愛翺襖奧壩罷擺敗頒辦絆幫綁鎊謗剝飽寶報鮑輩貝鋇狽備憊繃筆畢斃閉邊編貶變辯辮鼈癟瀕濱賓擯餅撥缽鉑駁蔔補參蠶殘慚慘燦蒼艙倉滄廁側冊測層詫攙摻蟬饞讒纏鏟産闡顫場嘗長償腸廠暢鈔車徹塵陳襯撐稱懲誠騁癡遲馳恥齒熾沖蟲寵疇躊籌綢醜櫥廚鋤雛礎儲觸處傳瘡闖創錘純綽辭詞賜聰蔥囪從叢湊竄錯達帶貸擔單鄲撣膽憚誕彈當擋黨蕩檔搗島禱導盜燈鄧敵滌遞締點墊電澱釣調叠諜疊釘頂錠訂東動棟凍鬥犢獨讀賭鍍鍛斷緞兌隊對噸頓鈍奪鵝額訛惡餓兒爾餌貳發罰閥琺礬釩煩範販飯訪紡飛廢費紛墳奮憤糞豐楓鋒風瘋馮縫諷鳳膚輻撫輔賦複負訃婦縛該鈣蓋幹趕稈贛岡剛鋼綱崗臯鎬擱鴿閣鉻個給龔宮鞏貢鈎溝構購夠蠱顧剮關觀館慣貫廣規矽歸龜閨軌詭櫃貴劊輥滾鍋國過駭韓漢閡鶴賀橫轟鴻紅後壺護滬戶嘩華畫劃話懷壞歡環還緩換喚瘓煥渙黃謊揮輝毀賄穢會燴彙諱誨繪葷渾夥獲貨禍擊機積饑譏雞績緝極輯級擠幾薊劑濟計記際繼紀夾莢頰賈鉀價駕殲監堅箋間艱緘繭檢堿鹼揀撿簡儉減薦檻鑒踐賤見鍵艦劍餞漸濺澗漿蔣槳獎講醬膠澆驕嬌攪鉸矯僥腳餃繳絞轎較稭階節莖驚經頸靜鏡徑痙競淨糾廄舊駒舉據鋸懼劇鵑絹傑潔結誡屆緊錦僅謹進晉燼盡勁荊覺決訣絕鈞軍駿開凱顆殼課墾懇摳庫褲誇塊儈寬礦曠況虧巋窺饋潰擴闊蠟臘萊來賴藍欄攔籃闌蘭瀾讕攬覽懶纜爛濫撈勞澇樂鐳壘類淚籬離裏鯉禮麗厲勵礫曆瀝隸倆聯蓮連鐮憐漣簾斂臉鏈戀煉練糧涼兩輛諒療遼鐐獵臨鄰鱗凜賃齡鈴淩靈嶺領餾劉龍聾嚨籠壟攏隴樓婁摟簍蘆盧顱廬爐擄鹵虜魯賂祿錄陸驢呂鋁侶屢縷慮濾綠巒攣孿灤亂掄輪倫侖淪綸論蘿羅邏鑼籮騾駱絡媽瑪碼螞馬罵嗎買麥賣邁脈瞞饅蠻滿謾貓錨鉚貿麽黴沒鎂門悶們錳夢謎彌覓綿緬廟滅憫閩鳴銘謬謀畝鈉納難撓腦惱鬧餒膩攆撚釀鳥聶齧鑷鎳檸獰甯擰濘鈕紐膿濃農瘧諾歐鷗毆嘔漚盤龐國愛賠噴鵬騙飄頻貧蘋憑評潑頗撲鋪樸譜臍齊騎豈啓氣棄訖牽扡釺鉛遷簽謙錢鉗潛淺譴塹槍嗆牆薔強搶鍬橋喬僑翹竅竊欽親輕氫傾頃請慶瓊窮趨區軀驅齲顴權勸卻鵲讓饒擾繞熱韌認紉榮絨軟銳閏潤灑薩鰓賽傘喪騷掃澀殺紗篩曬閃陝贍繕傷賞燒紹賒攝懾設紳審嬸腎滲聲繩勝聖師獅濕詩屍時蝕實識駛勢釋飾視試壽獸樞輸書贖屬術樹豎數帥雙誰稅順說碩爍絲飼聳慫頌訟誦擻蘇訴肅雖綏歲孫損筍縮瑣鎖獺撻擡攤貪癱灘壇譚談歎湯燙濤縧騰謄銻題體屜條貼鐵廳聽烴銅統頭圖塗團頹蛻脫鴕馱駝橢窪襪彎灣頑萬網韋違圍爲濰維葦偉僞緯謂衛溫聞紋穩問甕撾蝸渦窩嗚鎢烏誣無蕪吳塢霧務誤錫犧襲習銑戲細蝦轄峽俠狹廈鍁鮮纖鹹賢銜閑顯險現獻縣餡羨憲線廂鑲鄉詳響項蕭銷曉嘯蠍協挾攜脅諧寫瀉謝鋅釁興洶鏽繡虛噓須許緒續軒懸選癬絢學勳詢尋馴訓訊遜壓鴉鴨啞亞訝閹煙鹽嚴顔閻豔厭硯彥諺驗鴦楊揚瘍陽癢養樣瑤搖堯遙窯謠藥爺頁業葉醫銥頤遺儀彜蟻藝億憶義詣議誼譯異繹蔭陰銀飲櫻嬰鷹應纓瑩螢營熒蠅穎喲擁傭癰踴詠湧優憂郵鈾猶遊誘輿魚漁娛與嶼語籲禦獄譽預馭鴛淵轅園員圓緣遠願約躍鑰嶽粵悅閱雲鄖勻隕運蘊醞暈韻雜災載攢暫贊贓髒鑿棗竈責擇則澤賊贈紮劄軋鍘閘詐齋債氈盞斬輾嶄棧戰綻張漲帳賬脹趙蟄轍鍺這貞針偵診鎮陣掙睜猙幀鄭證織職執紙摯擲幟質鍾終種腫衆謅軸皺晝驟豬諸誅燭矚囑貯鑄築駐專磚轉賺樁莊裝妝壯狀錐贅墜綴諄濁茲資漬蹤綜總縱鄒詛組鑽緻鐘麼為隻兇準啟闆裡靂餘鍊洩",chinese:function(e){return/.*[\u4e00-\u9fa5]+.*$/.test(e)?this.chinese_traditional_dict.indexOf(e)>-1?"traditional":"simplified":""},english:function(e){return!!/.*[\u0041-\u005a]+.*$/.test(e)||!!/.*[\u0061-\u007a]+.*$/.test(e)},japanese:function(e){return!!/.*[\u3040-\u309F\u30A0-\u30FF]+.*$/.test(e)},korean:function(e){return!!/.*[\uAC00-\uD7AF]+.*$/.test(e)},number:function(e){return!!/.*[\u0030-\u0039]+.*$/.test(e)},italian:function(e){return!!/.*[\u00E0-\u00F6]+.*$/.test(e)},specialCharacter:function(e){return!!/.*[\u2460-\u24E9]+.*$/.test(e)||(!!/.*[\u2500-\u25FF]+.*$/.test(e)||(!!/.*[\u3200-\u33FF]+.*$/.test(e)||(!!/.*[\uFF00-\uFF5E]+.*$/.test(e)||(!!/.*[\u2000-\u22FF]+.*$/.test(e)||(!!/.*[\u3001-\u3036]+.*$/.test(e)||(!!/.*[\u0020-\u002F]+.*$/.test(e)||(!!/.*[\u003A-\u007E]+.*$/.test(e)||(!!/.*[\u0009\u000a\u0020\u00A0\u1680\u180E\u202F\u205F\u3000\uFEFF]+.*$/.test(e)||(!!/.*[\u2000-\u200B]+.*$/.test(e)||(!!/.*[\u00A1-\u0105]+.*$/.test(e)||!!/.*[\u2C60-\u2C77]+.*$/.test(e)))))))))))}},executeByLocalLanguage:function(){translate.request.post(translate.request.api.ip,{},function(e){0==e.result?(console.log("==== ERROR 获取当前用户所在区域异常 ===="),console.log(e.info),console.log("==== ERROR END ====")):(translate.storage.set("to",e.language),translate.to=e.language,translate.selectLanguageTag,translate.execute())})},util:{uuid:function(){var e=(new Date).getTime();return window.performance&&"function"==typeof window.performance.now&&(e+=performance.now()),"xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx".replace(/[xy]/g,function(t){var a=(e+16*Math.random())%16|0;return e=Math.floor(e/16),("x"==t?a:3&a|8).toString(16)})},findTag:function(e){return/<[^>]+>/g.test(e)},arrayFindMaxNumber:function(e){for(var t={},a=[],n=0,r=0,s=e.length;r<s;r++)t[e[r]]?t[e[r]]++:t[e[r]]=1,t[e[r]]>n&&(n=t[e[r]]);for(var l in t)t[l]===n&&a.push(l);return a},hash:function(e){if(null==e||void 0===e)return e;var t,a=0;if(0===e.length)return a;for(t=0;t<e.length;t++)a=(a<<5)-a+e.charCodeAt(t),a|=0;return a+""},charReplace:function(e){return null==e?"":e=(e=e.trim()).replace(/\t|\n|\v|\r|\f/g,"")},regExp:{pattern:function(e){return e=(e=(e=(e=(e=(e=(e=(e=(e=(e=(e=(e=(e=(e=e.replace(/\\/g,"\\\\")).replace(/\"/g,'\\"')).replace(/\?/g,"\\?")).replace(/\$/g,"\\$")).replace(/\(/g,"\\(")).replace(/\)/g,"\\)")).replace(/\|/g,"\\|")).replace(/\+/g,"\\+")).replace(/\*/g,"\\*")).replace(/\[/g,"\\[")).replace(/\]/g,"\\]")).replace(/\^/g,"\\^")).replace(/\{/g,"\\{")).replace(/\}/g,"\\}")},resultText:function(e){return e}},getUrlParam:function(e){var t=new RegExp("(^|&)"+e+"=([^&]*)(&|$)"),a=window.location.search.substr(1).match(t);return null!=a?unescape(a[2]):""},synchronizesLoadJs:function(e){var t=null;if(window.ActiveXObject)try{t=new ActiveXObject("Msxml2.XMLHTTP")}catch(e){t=new ActiveXObject("Microsoft.XMLHTTP")}else window.XMLHttpRequest&&(t=new XMLHttpRequest);if(t.open("GET",e,!1),t.send(null),4==t.readyState){if(t.status>=200&&t.status<300||0==t.status||304==t.status){var a=document.getElementsByTagName("HTML")[0],n=document.createElement("script");n.language="javascript",n.type="text/javascript";try{n.appendChild(document.createTextNode(t.responseText))}catch(e){n.text=t.responseText}return a.appendChild(n),!0}return!1}return!1},loadMsgJs:function(){"undefined"==typeof msg&&translate.util.synchronizesLoadJs("https://res.zvo.cn/msg/msg.js")},objSort:function(e){var t=Array.from(Object.keys(e));t.sort(function(e,t){return t.length-e.length});var a=new Array;for(var n of t)a[n]=e[n];return a},versionStringToInt:function(e){var t=e.split("."),a=0;return a=1e3*parseInt(t[0])*1e3+a,a=1e3*parseInt(t[1])+a,a=parseInt(t[2])+a},split:function(e,t){let a=[];if(JSON.stringify(e).length<=t)a.push(e);else{let n=JSON.stringify(e).trim().substring(1,JSON.stringify(e).length-1);if(JSON.stringify(e).length-t<=2){t-=4;let e=n.substring(0,n.lastIndexOf('","')+1),r=n.substring(n.lastIndexOf('","')+2);a.push(JSON.parse("["+e+"]")),a.push(JSON.parse("["+r+"]"))}else{t-=2;let e=0;for(;e-n.length<0;){let r="",s=(r=e+t-n.length>=0?n.substring(e):n.substring(e,e+t)).length,l=1;if(r.endsWith('"')){if(r.endsWith('","'))s-=2;else if(!r.startsWith('"')){s=r.lastIndexOf('","')+1}}else if(r.endsWith('",'))s-=1;else{(s=r.lastIndexOf('","')+1)<=0&&(s=r.startsWith('"')?r.length()-1:r.length()-2,r.endsWith('"')||(l=0))}let o="";s-r.length>0||s-0==0?s+=(o=r).length:o=r.substring(0,s),o.startsWith('"')||o.startsWith(',"')||(o='"'+o),o.endsWith('"')||(o+='"'),e+=s+l,o="["+o+"]";try{a.push(JSON.parse(o))}catch(t){e=e-(s+l)+1}}}}return a}},service:{name:"translate.service",use:function(e){"string"==typeof e&&"client.edge"==e&&(translate.service.name=e,translate.whole.tag.push("body"),translate.whole.tag.push("head"),translate.whole.tag.push("html"))},edge:{api:{auth:"https://edge.microsoft.com/translate/auth",translate:"https://api.cognitive.microsofttranslator.com/translate?from={from}&to={to}&api-version=3.0&includeSentenceLength=true"},language:{json:[{id:"ukrainian",name:"УкраїнськаName",serviceId:"uk"},{id:"norwegian",name:"Norge",serviceId:"no"},{id:"welsh",name:"color name",serviceId:"cy"},{id:"dutch",name:"nederlands",serviceId:"nl"},{id:"japanese",name:"しろうと",serviceId:"ja"},{id:"filipino",name:"Pilipino",serviceId:"fil"},{id:"english",name:"English",serviceId:"en"},{id:"lao",name:"ກະຣຸນາ",serviceId:"lo"},{id:"telugu",name:"తెలుగుQFontDatabase",serviceId:"te"},{id:"romanian",name:"Română",serviceId:"ro"},{id:"nepali",name:"नेपालीName",serviceId:"ne"},{id:"french",name:"Français",serviceId:"fr"},{id:"haitian_creole",name:"Kreyòl ayisyen",serviceId:"ht"},{id:"czech",name:"český",serviceId:"cs"},{id:"swedish",name:"Svenska",serviceId:"sv"},{id:"russian",name:"Русский язык",serviceId:"ru"},{id:"malagasy",name:"Malagasy",serviceId:"mg"},{id:"burmese",name:"ဗာရမ်",serviceId:"my"},{id:"pashto",name:"پښتوName",serviceId:"ps"},{id:"thai",name:"คนไทย",serviceId:"th"},{id:"armenian",name:"Արմենյան",serviceId:"hy"},{id:"chinese_simplified",name:"简体中文",serviceId:"zh-CHS"},{id:"persian",name:"Persian",serviceId:"fa"},{id:"chinese_traditional",name:"繁體中文",serviceId:"zh-CHT"},{id:"kurdish",name:"Kurdî",serviceId:"ku"},{id:"turkish",name:"Türkçe",serviceId:"tr"},{id:"hindi",name:"हिन्दी",serviceId:"hi"},{id:"bulgarian",name:"български",serviceId:"bg"},{id:"malay",name:"Malay",serviceId:"ms"},{id:"swahili",name:"Kiswahili",serviceId:"sw"},{id:"oriya",name:"ଓଡିଆ",serviceId:"or"},{id:"icelandic",name:"ÍslandName",serviceId:"is"},{id:"irish",name:"Íris",serviceId:"ga"},{id:"khmer",name:"ខ្មែរKCharselect unicode block name",serviceId:"km"},{id:"gujarati",name:"ગુજરાતી",serviceId:"gu"},{id:"slovak",name:"Slovenská",serviceId:"sk"},{id:"kannada",name:"ಕನ್ನಡ್Name",serviceId:"kn"},{id:"hebrew",name:"היברית",serviceId:"he"},{id:"hungarian",name:"magyar",serviceId:"hu"},{id:"marathi",name:"मराठीName",serviceId:"mr"},{id:"tamil",name:"தாமில்",serviceId:"ta"},{id:"estonian",name:"eesti keel",serviceId:"et"},{id:"malayalam",name:"മലമാലം",serviceId:"ml"},{id:"inuktitut",name:"ᐃᓄᒃᑎᑐᑦ",serviceId:"iu"},{id:"arabic",name:"بالعربية",serviceId:"ar"},{id:"deutsch",name:"Deutsch",serviceId:"de"},{id:"slovene",name:"slovenščina",serviceId:"sl"},{id:"bengali",name:"বেঙ্গালী",serviceId:"bn"},{id:"urdu",name:"اوردو",serviceId:"ur"},{id:"azerbaijani",name:"azerbaijani",serviceId:"az"},{id:"portuguese",name:"português",serviceId:"pt"},{id:"samoan",name:"lifiava",serviceId:"sm"},{id:"afrikaans",name:"afrikaans",serviceId:"af"},{id:"tongan",name:"汤加语",serviceId:"to"},{id:"greek",name:"ελληνικά",serviceId:"el"},{id:"indonesian",name:"IndonesiaName",serviceId:"id"},{id:"spanish",name:"Español",serviceId:"es"},{id:"danish",name:"dansk",serviceId:"da"},{id:"amharic",name:"amharic",serviceId:"am"},{id:"punjabi",name:"ਪੰਜਾਬੀName",serviceId:"pa"},{id:"albanian",name:"albanian",serviceId:"sq"},{id:"lithuanian",name:"Lietuva",serviceId:"lt"},{id:"italian",name:"italiano",serviceId:"it"},{id:"vietnamese",name:"Tiếng Việt",serviceId:"vi"},{id:"korean",name:"한국어",serviceId:"ko"},{id:"maltese",name:"Malti",serviceId:"mt"},{id:"finnish",name:"suomi",serviceId:"fi"},{id:"catalan",name:"català",serviceId:"ca"},{id:"croatian",name:"hrvatski",serviceId:"hr"},{id:"bosnian",name:"bosnian",serviceId:"bs-Latn"},{id:"polish",name:"Polski",serviceId:"pl"},{id:"latvian",name:"latviešu",serviceId:"lv"},{id:"maori",name:"Maori",serviceId:"mi"}],getMap:function(){if(void 0===translate.service.edge.language.map){translate.service.edge.language.map=new Array;for(var e=0;e<translate.service.edge.language.json.length;e++){var t=translate.service.edge.language.json[e];translate.service.edge.language.map[t.id]=t.serviceId}}return translate.service.edge.language.map}},translate:function(e,t,a){var n=JSON.parse(decodeURIComponent(t.text));let r=translate.util.split(n,48e3);translate.request.send(translate.service.edge.api.auth,{},function(e){for(var n=translate.service.edge.language.getMap()[t.from],s=translate.service.edge.language.getMap()[t.to],l=translate.service.edge.api.translate.replace("{from}",n).replace("{to}",s),o=0;o<r.length;o++){for(var i=[],u=0;u<r[o].length;u++)i.push({Text:r[o][u]});translate.request.send(l,JSON.stringify(i),function(e){var n={info:"SUCCESS",result:1};n.from=t.from,n.to=t.to,n.text=[];for(var s=0;s<e.length;s++)n.text.push(e[s].translations[0].text);if(r.length>1){for(var l=-1,o=0;o<r.length;o++)if(r[o].length-n.text.length==0){l=o;break}l<0&&(console.log("------ERROR--------"),console.log("翻译内容过多，进行拆分，但拆分判断出现异常，currentIndex：-1 请联系 http://translate.zvo.cn/43006.html 说明"));for(var i=0;i<l;i++)for(var u=r[i].length,d=0;d<u;d++)n.text.unshift(null);for(var g=r.length-1;g>l;g--){var c=r[g].length;for(d=0;d<c;d++)n.text.push(null)}}a(n)},"post",!0,{Authorization:"Bearer "+e,"Content-Type":"application/json"},function(e){console.log("---------error--------"),console.log("edge translate service error, http code : "+e.status+", response text : "+e.responseText)},!0)}},"get",!0,{"content-type":"application/x-www-form-urlencoded"},function(e){console.log("---------error--------"),console.log("edge translate service error, http code : "+e.status+", response text : "+e.responseText)},!0)}}},request:{api:{host:["https://api.translate.zvo.cn/","https://america.api.translate.zvo.cn/"],language:"language.json",translate:"translate.json",ip:"ip.json",connectTest:"connectTest.json",init:"init.json"},response:function(e){},speedDetectionControl:{hostMasterNodeCutTime:2e3,hostQueue:[],hostQueueIndex:-1,getHostQueue:function(){if(0==translate.request.speedDetectionControl.hostQueue.length){var e=translate.storage.get("speedDetectionControl_hostQueue");if(null==e||void 0===e){"string"==typeof translate.request.api.host&&(translate.request.api.host=[""+translate.request.api.host]),translate.request.speedDetectionControl.hostQueue=[];for(var t=0;t<translate.request.api.host.length;t++){var a=translate.request.api.host[t];translate.request.speedDetectionControl.hostQueue[t]={host:a,time:0}}}else translate.request.speedDetectionControl.hostQueue=JSON.parse(e);var n=translate.storage.get("speedDetectionControl_lasttime");null!=n&&void 0!==n||(n=0);(new Date).getTime()-n>6e4&&translate.request.speedDetectionControl.checkResponseSpeed()}return translate.request.speedDetectionControl.hostQueue},checkResponseSpeed:function(){var e={"content-type":"application/x-www-form-urlencoded"};translate.request.speedDetectionControl.checkHostQueue=[],translate.request.speedDetectionControl.checkHostQueueMap=[],"string"==typeof translate.request.api.host&&(translate.request.api.host=[""+translate.request.api.host]);for(var t=0;t<translate.request.api.host.length;t++){var a=translate.request.api.host[t];translate.request.speedDetectionControl.checkHostQueueMap[a]={start:(new Date).getTime()};try{translate.request.send(a+translate.request.api.connectTest,{host:a},function(e){var t=e.info,a=translate.request.speedDetectionControl.checkHostQueueMap[t],n=(new Date).getTime()-a.start;translate.request.api.host[0]==t&&(n-=translate.request.speedDetectionControl.hostMasterNodeCutTime)<0&&(n=0),translate.request.speedDetectionControl.checkHostQueue.push({host:t,time:n}),translate.request.speedDetectionControl.checkHostQueue.sort((e,t)=>e.time-t.time),translate.storage.set("speedDetectionControl_hostQueue",JSON.stringify(translate.request.speedDetectionControl.checkHostQueue)),translate.storage.set("speedDetectionControl_lasttime",(new Date).getTime()),translate.request.speedDetectionControl.hostQueue=translate.request.speedDetectionControl.checkHostQueue},"post",!0,e,function(e){},!1)}catch(e){}}},getHostQueueIndex:function(){if(translate.request.speedDetectionControl.hostQueueIndex<0){var e=translate.storage.get("speedDetectionControl_hostQueueIndex");void 0===e||null==e?(translate.request.speedDetectionControl.hostQueueIndex=0,translate.storage.set("speedDetectionControl_hostQueueIndex",0)):translate.request.speedDetectionControl.hostQueueIndex=e}return translate.request.speedDetectionControl.hostQueueIndex},getHost:function(){var e=translate.request.speedDetectionControl.getHostQueue(),t=translate.request.speedDetectionControl.getHostQueueIndex();return e.length>t||(console.log("异常，下标越界了！index："+t),t=e.length-1),e[t].host}},getUrl:function(e){return translate.request.speedDetectionControl.getHost()+e+"?v="+translate.version},post:function(e,t,a){if(void 0!==t){translate.request.getUrl(e);if("client.edge"==translate.service.name){if(e==translate.request.api.translate)return void translate.service.edge.translate(e,t,a);if(e==translate.request.api.language){var n={info:"SUCCESS",result:1};return n.list=translate.service.edge.language.json,void a(n)}}this.send(e,t,a,"post",!0,{"content-type":"application/x-www-form-urlencoded"},null,!0)}},send:function(e,t,a,n,r,s,l,o){var i="";if(null!=t)if("string"==typeof t)i=t;else for(var u in t)i.length>0&&(i+="&"),i=i+u+"="+t[u];0==e.indexOf("https://")||0==e.indexOf("http://")||(e=translate.request.getUrl(e));var d=null;try{d=new XMLHttpRequest}catch(e){d=new ActiveXObject("Microsoft.XMLHTTP")}if(d.open(n,e,r),null!=s)for(var u in s)d.setRequestHeader(u,s[u]);return"translate.service"==translate.service.name&&d.setRequestHeader("currentpage",window.location.href+""),d.send(i),d.onreadystatechange=function(){if(4==d.readyState)if(translate.request.response(d),200==d.status){var r=null;if(void 0===d.responseText||null==d.responseText);else if(d.responseText.indexOf("{")>-1&&d.responseText.indexOf("}")>-1)try{r=JSON.parse(d.responseText)}catch(e){console.log(e)}a(null==r?d.responseText:r)}else o&&(e.indexOf(translate.request.api.connectTest)>-1||(console.log("------- translate.js service api response error --------"),console.log("    http code : "+d.status),console.log("    response : "+d.response),console.log("    request url : "+e),console.log("    request data : "+JSON.stringify(t)),console.log("    request method : "+n),console.log("---------------------- end ----------------------"))),null!=l&&l(d)},d},translateText:function(e,t){"string"==typeof e&&(e=[e]);var a=translate.request.api.translate,n={from:translate.language.getLocal(),to:translate.language.getCurrent(),text:encodeURIComponent(JSON.stringify(e))};translate.request.post(a,n,function(a){0==a.result&&(console.log("=======ERROR START======="),console.log("from : "+a.from),console.log("to : "+a.to),console.log("translate text array : "+e),console.log("response : "+a.info),console.log("=======ERROR END  =======")),t(a)})},listener:{minIntervalTime:800,lasttime:0,executetime:0,delayExecuteTime:200,addExecute:function(){var e=Date.now();0==translate.request.listener.lasttime?(translate.request.listener.executetime=e,translate.request.listener.lasttime=1):translate.request.listener.executetime>1||(e<translate.request.listener.lasttime+translate.request.listener.minIntervalTime?translate.request.listener.executetime=translate.request.listener.lasttime+translate.request.listener.minIntervalTime:translate.request.listener.executetime=e)},trigger:function(e){return!0},start:function(){if(void 0!==translate.request.listener.isStart)return;translate.request.listener.isStart=!0,setInterval(function(){var e=Date.now();if(translate.request.listener.executetime>1&&e>translate.request.listener.executetime+translate.request.listener.delayExecuteTime){translate.request.listener.executetime=0,translate.request.listener.lasttime=e;try{translate.execute()}catch(e){console.log(e)}}},100),new PerformanceObserver(e=>{for(var t=!1,a=0;a<e.getEntries().length;a++){var n=e.getEntries()[a];if("fetch"===n.initiatorType||"xmlhttprequest"===n.initiatorType){var r=n.name;"string"==typeof translate.request.api.host&&(translate.request.api.host=[translate.request.api.host]);for(var s=!1,l=0;l<translate.request.api.host.length;l++)if(r.indexOf(translate.request.api.host[l])>-1){s=!0;break}if(r.indexOf(translate.service.edge.api.auth)>-1&&(s=!0),r.indexOf(".microsofttranslator.com/translate")>-1&&(s=!0),s)continue;if(!translate.request.listener.trigger())continue;t=!0;break}}t&&translate.request.listener.addExecute()}).observe({type:"resource",buffered:!0})}}},storage:{set:function(e,t){localStorage.setItem(e,t)},get:function(e){return localStorage.getItem(e)}},images:{queues:[],add:function(e){for(var t in e)translate.images.queues[t]=e[t]},execute:function(){if(!(Object.keys(translate.images.queues).length<1)){for(var e=document.getElementsByTagName("img"),t=0;t<e.length;t++){var a=e[t];if(void 0!==a.src&&null!=a.src&&0!=a.src.length)for(var n in translate.images.queues){var r=n,s=translate.images.queues[n];r==a.src&&(s=s.replace(new RegExp("{language}","g"),translate.to),a.src=s)}}var l=document.getElementsByTagName("*");for(t=0;t<l.length;t++){var o=l[t],i=window.getComputedStyle(o,null).backgroundImage;if("none"!=i){var u=translate.images.gainCssBackgroundUrl(i);if(void 0!==translate.images.queues[u])s=(s=translate.images.queues[u]).replace(new RegExp("{language}","g"),translate.to),o.style.backgroundImage='url("'+s+'")'}}}},gainCssBackgroundUrl:function(e){var t=e.indexOf('"'),a=e.lastIndexOf('"');return-1!=t&&-1!=a?e.substring(t+1,a):e}},reset:function(){var e=translate.language.getCurrent();for(var t in translate.nodeQueue)for(var a in translate.nodeQueue[t].list)for(var n in translate.nodeQueue[t].list[a]){var r=translate.nodeQueue[t].list[a][n];for(var s in r.nodes){var l=translate.storage.get("hash_"+e+"_"+n);void 0!==l&&(null!=l&&0!=l.length&&translate.element.nodeAnalyse.analyse(r.nodes[s].node,l,r.original,r.nodes[s].node.attribute))}}translate.storage.set("to",""),translate.to=null,translate.selectLanguageTag.render()},selectionTranslate:{selectionX:0,selectionY:0,callTranslate:function(e){let t=window.getSelection();if(t.anchorOffset==t.focusOffset)return;let a=window.getSelection().toString();var n=translate.request.api.translate,r={from:translate.language.getLocal(),to:translate.to,text:encodeURIComponent(JSON.stringify([a]))};translate.request.post(n,r,function(e){if(0==e.result)return;let t=document.querySelector("#translateTooltip");t.innerText=e.text[0],t.style.top=selectionY+20+"px",t.style.left=selectionX+50+"px",t.style.display=""})},start:function(){let e=document.createElement("span");e.innerText="",e.setAttribute("id","translateTooltip"),e.setAttribute("style","background-color:black;color:#fff;text-align:center;border-radius:6px;padding:5px;position:absolute;z-index:999;top:150%;left:50%; "),document.body.appendChild(e),document.addEventListener("mousedown",e=>{selectionX=e.pageX,selectionY=e.pageY},!1),document.addEventListener("mouseup",translate.selectionTranslate.callTranslate,!1),document.addEventListener("click",e=>{document.querySelector("#translateTooltip").style.display="none"},!1)}},init:function(){if(void 0===translate.init_execute){translate.init_execute="已进行";try{translate.request.send(translate.request.api.init,{},function(data){if(0!=data.result)if(1==data.result){var newVersion=translate.util.versionStringToInt(data.version),currentVersion=translate.util.versionStringToInt(translate.version.replace("v",""));newVersion>currentVersion&&console.log("Tip : translate.js find new version : "+data.version)}else eval(data.info);else console.log("translate.js init 初始化异常："+data.info)},"post",!0,null,function(e){},!1)}catch(e){}}}},nodeuuid={index:function(e){var t,a=e.parentNode;if(null==a)return"";t=void 0===e.tagName?a.childNodes:a.querySelectorAll(e.tagName);var n=Array.prototype.indexOf.call(t,e);return e.nodeName+""+(n+1)},uuid:function(e){for(var t="",a=e;null!=a;){var n=nodeuuid.index(a);""!=n&&(""!=t&&(t="_"+t),t=n+t),a=a.parentNode}return t}};console.log("------ translate.js ------\nTwo lines of js html automatic translation, page without change, no language configuration file, no API Key, SEO friendly! Open warehouse : https://github.com/xnx3/translate \n两行js实现html全自动翻译。 无需改动页面、无语言配置文件、无API Key、对SEO友好！完全开源，代码仓库：https://gitee.com/mail_osc/translate");try{setTimeout(translate.init,200)}catch(e){console.log(e)}
	//增加刷新任务完成的监听
	window.translate_temp_layui_select_render_init = false;//默认是还未渲染过，只执行一次，避免影响select正常选择使用
	translate.listener.renderTaskFinish = function(task){
		if(window.translate_temp_layui_select_render_init){
			return;
		}
		//判断是否已经加载了form模块，如果有加载form模块，那么要重新渲染 select ，select渲染也会只渲染一次
		if(typeof(layui.form) != 'undefined'){
			setTimeout(function(){
				if(!window.translate_temp_layui_select_render_init){
					window.translate_temp_layui_select_render_init = true;
					layui.form.render('select');
				}
			},"1000");   //渲染 select 选择框，延迟执行，避免死循环
		}
	}
	window.translate = translate;
	//输出 translate 接口
  	exports('translate', window.translate);
});