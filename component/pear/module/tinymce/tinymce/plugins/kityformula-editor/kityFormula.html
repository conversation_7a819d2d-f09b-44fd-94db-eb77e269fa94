<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="renderer" content="webkit">
    <link rel="stylesheet" href="kityformula/assets/styles/base.css">
    <link rel="stylesheet" href="kityformula/assets/styles/ui.css">
    <link rel="stylesheet" href="kityformula/assets/styles/scrollbar.css">
    <style>
        html, body {
            padding: 0;
            margin: 0;
        }
        .kf-editor {
            width: 780px;
            height: 380px;
        }
        #loading {
            height: 32px;
            width: 340px;
            line-height: 32px;
            position: absolute;
            top: 42%;
            left: 50%;
            margin-left: -170px;
            font-family: arial, "Hiragino Sans GB", "Microsoft YaHei", "WenQuanYi Micro Hei", sans-serif;
        }
        #loading img {
            position: absolute;
        }
        #loading p {
            display: block;
            position: absolute;
            left: 40px;
            top: 0px;
            margin: 0;
        }

    </style>
    <title></title>
</head>
<body>
    <div id="kfEditorContainer" class="kf-editor">
        <div id="tips" class="tips">
            sorry! Beta版本仅支持IE9及以上版本的浏览器，正式版本将会支持低版本浏览器，谢谢您的关注！
        </div>
    </div>

<script src="kityformula/js/jquery-3.6.0.min.js"></script>
<script src="kityformula/js/kitygraph.all.js"></script>
<script src="kityformula/js/kity-formula-render.all.js"></script>
<script src="kityformula/js/kity-formula-parser.all.min.js"></script>
<script src="kityformula/js/kityformula-editor.all.min.js"></script>
<script>
//获取url地址栏传参
function getQueryVariable(variable){
    var query = window.location.search.substring(1);
    var vars = query.split("&");
    for (var i=0;i<vars.length;i++) {
            var pair = vars[i].split("=");
            if(pair[0] == variable){return pair[1];}
    }
    return(false);
}
//处理TinyMCE编辑器的事件
window.addEventListener('message', function (event) {
  var data = event.data;
  kfe.execCommand('get.image.data', function(data){
        var latex = kfe.execCommand('get.source');
        window.parent.postMessage({
            mceAction: 'insertContent',
            content: "<img src=\""+data.img+"\" data-latex=\""+latex+"\">"
        }, '*');
        window.parent.postMessage({
            mceAction: 'close'
        }, '*');
    });
});
//实例化编辑器
jQuery( function ($) {
    if ( document.body.addEventListener ) {
        $( "#tips").html('<div id="loading"><img src="kityformula/loading.gif" alt="loading" /><p>正在加载，请耐心等待...</p></div>' );
        var factory = kf.EditorFactory.create( $( "#kfEditorContainer" )[ 0 ], {
            render: {
                fontsize: 24
            },
            resource: {
                path: "./kityformula/resource/"
            }
        } );
        factory.ready( function ( KFEditor ) {
            $( "#tips").remove();
            //处理地址栏的参数并加载到编辑器中
            var c=getQueryVariable("c")
            if(c){
                this.execCommand( "render",decodeURIComponent(c) );
            }else{
                this.execCommand( "render","\\placeholder" );
            };

            this.execCommand( "focus" );
            window.kfe = this;
        } );
    } else {
        $( "#tips").css( "color", "black" );
        $( "#tips").css( "padding", "10px" );
    }
} );
</script>
</body>
</html>