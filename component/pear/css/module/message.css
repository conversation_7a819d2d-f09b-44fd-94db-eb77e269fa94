.pear-notice .layui-this {
	color: #5FB878 !important;
	font-weight: 500;
}

.pear-notice {
	box-shadow: 0 6px 16px -8px rgb(0 0 0 / 8%), 0 9px 28px 0 rgb(0 0 0 / 5%), 0 12px 48px 16px rgb(0 0 0 / 3%)!important;
}

.pear-notice .layui-tab-title {
	display: flex;
	text-align: center;
	border-right: 1px solid whitesmoke;
}

.pear-notice .layui-tab-title li {
	flex: 1;
	text-align: center;
	border-right: 1px solid whitesmoke;
}
/*排除最后一个 li 右边框*/
.pear-notice .layui-tab-title li:last-child {
    border-right: none;
}

.pear-notice * {
	color: dimgray !important;
}

.pear-notice {
	width: 360px !important;
}

.pear-notice img {
	margin-left: 8px;
	width: 33px !important;
	height: 33px !important;
	border-radius: 50px;
	margin-right: 15px;
}

.pear-notice-item {
	height: 45px !important;
	line-height: 45px !important;
	padding-right: 20px;
	padding-left: 20px;
	border-bottom: 1px solid whitesmoke;
	padding-top: 10px;
	padding-bottom: 15px;
}
.pear-notice-end {
	float: right;
	right: 10px;
}

.pear-notice-item span{
	height: 40px;
	line-height: 40px;
}

/** 滚动条样式 */
.pear-notice *::-webkit-scrollbar {
	width: 0px;
	height: 0px;
}

.pear-notice *::-webkit-scrollbar-track {
	background: white;
	border-radius: 2px;
}

.pear-notice *::-webkit-scrollbar-thumb {
	background: #E6E6E6;
	border-radius: 2px;
}

.pear-notice *::-webkit-scrollbar-thumb:hover {
	background: #E6E6E6;
}

.pear-notice *::-webkit-scrollbar-corner {
	background: #f6f6f6;
}
/** 增加 empty 样式 */
.pear-empty {
	font-size: 14px;
	line-height: 1.5715;
	min-height: 200px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
}
.pear-empty-normal {
	margin: 32px 0;
	color: #00000040;
}
.pear-empty-normal .pear-empty-image {
	height: 40px;
}

.pear-empty-image {
	height: 100px;
	margin-bottom: 8px;
}
.pear-empty-image svg {
	height: 100%;
	margin: auto;
}

.pear-empty-img-simple-g {
	stroke: #d9d9d9;
}
.pear-empty-img-default-g {
	fill: #fff;
}
.pear-empty-img-simple-path {
	fill: #fafafa;
}
.pear-empty-img-default-path-1 {
	fill: #aeb8c2;
}
.pear-empty-img-default-path-2 {
	fill: url(#linearGradient-1);
}
.pear-empty-img-default-path-3 {
	fill: #f5f5f7;
}
.pear-empty-img-default-path-4, .pear-empty-img-default-path-5 {
	fill: #dce0e6;
}


