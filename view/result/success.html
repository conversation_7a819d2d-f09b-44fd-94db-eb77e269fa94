<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>成功</title>
		<link rel="stylesheet" href="../../component/pear/css/pear.css" />
	    <link rel="stylesheet" href="../../admin/css/other/result.css" />
	</head>
	<body class="pear-container">
		<div class="layui-card">
			<div class="layui-card-body">
				<div class="result">
					<div class="success">
					<svg viewBox="64 64 896 896" data-icon="check-circle" width="80px" height="80px" fill="currentColor" aria-hidden="true" focusable="false" class=""><path d="M699 353h-46.9c-10.2 0-19.9 4.9-25.9 13.3L469 584.3l-71.2-98.8c-6-8.3-15.6-13.3-25.9-13.3H325c-6.5 0-10.3 7.4-6.5 12.7l124.6 172.8a31.8 31.8 0 0 0 51.7 0l210.6-292c3.9-5.3.1-12.7-6.4-12.7z"></path><path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"></path></svg>
				    </div>
					<h2 class="title">提交成功</h2>
					<p class="desc">
						提交结果页用于反馈一系列操作任务的处理结果，
						如果仅是简单操作，使用 Message 全局提示反馈即可。
						本文字区域可以展示简单的补充说明，如果有类似展示
						“单据”的需求，下面这个灰色区域可以呈现比较复杂的内容。
					</p>
					<div class="content">
						
					</div>
					<div class="action">
						<button class="pear-btn pear-btn-primary">再次申请</button>
						&nbsp;&nbsp;&nbsp;
						<button class="pear-btn">返回首页</button>
					</div>
				</div>
			</div>
		</div>
		<script src="../../component/layui/layui.js"></script>
		<script src="../../component/pear/pear.js"></script>
	</body>
</html>
