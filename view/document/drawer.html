<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
		<title>抽屉组件</title>
		<link href="../../component/pear/css/pear.css" rel="stylesheet" />
		<style>
			.wrap {
				display: none;
			}
		</style>
	</head>
	<body class="pear-container">
		<div class="layui-row layui-col-space10">
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">开发环境</div>
					<div class="layui-card-body">
						Drawer 在 弹出层 UI 上带来不一样的体验.
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
								<script src="component/layui/layui.js"></script>
								 并
								<script src="component/pear/pear.js"></script>
							</pre>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">抽屉</div>
					<div class="layui-card-body">
						<button class="drawer-top pear-btn pear-btn-primary">上</button>
						<button class="drawer-bottom pear-btn pear-btn-success">下</button>
						<button class="drawer-left pear-btn pear-btn-warming">左</button>
						<button class="drawer-right pear-btn pear-btn-danger">右</button>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
								layui.use(['drawer'], function() {
									var drawer = layui.drawer;

								    drawer.open({
								        direction: "right",
								        dom: ".layer-top",
								        distance: "30%"
								    });
								})
							</pre>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">进阶</div>
					<div class="layui-card-body">
						<button class="drawer-close-mask pear-btn pear-btn-primary">手动关闭</button>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
								var dom;

								$(".drawer-close-mask").click(function() {
								    dom = drawer.open({
								        direction: "right",
								        dom: ".layer-close-mask",
								        distance: "30%",
								        maskClose: false
								    });
								})

								$("#btnClose").click(function() {
								    dom.close();
								})

							</pre>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">扩展</div>
					<div class="layui-card-body">
						<button class="drawer-auto-close pear-btn pear-btn-success">自动关闭</button>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
								layui.use(['drawer'], function() {
									var drawer = layui.drawer;

								    $(".drawer-auto-close").click(function() {
								        dom = drawer.open({
								            direction: "right",
								            dom: ".layer-auto-close",
								            distance: "30%",
								            maskClose: false,
								            time: 1000
								        });
								    })
								})
							</pre>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">扩展</div>
					<div class="layui-card-body">
						<button class="drawer-call-back pear-btn pear-btn-success">回调函数</button>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
								layui.use(['drawer'], function() {
									var drawer = layui.drawer;

								    $(".drawer-auto-close").click(function() {
								        dom = drawer.open({
								            direction: "right",
								            dom: ".layer-auto-close",
								            distance: "30%",
								            success:function(){
								                layer.msg("触发回调函数");
								            }
								        });
								    })
								})
							</pre>
						</div>
					</div>
				</div>
			</div>

			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">扩展</div>
					<div class="layui-card-body">
						<div id="targetEl"
								style="width: 600px;
								height: 300px;
								overflow: hidden;
								position: relative;
								border: 1px solid gainsboro;
								background-color: #EEE;
								margin-top: 20px;
								display: flex;
								justify-content: space-around;
								align-items: center">
								<button id="targetDemo" class="pear-btn pear-btn-success">指定容器内打开</button>
						</div>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
									layui.use(['drawer'], function() {
									  var drawer = layui.drawer;
									    $("#targetDemo").click(function () {
									      drawer.open({
									      // 指定挂载节点
									      target: "#targetEl"
									      direction: "right",
									      dom: ".layer-right",
									      distance: "50%",
									      });
									})
							</pre>
						</div>
					</div>
				</div>
			</div>
			<!-- 新版 API -->
			<div class="layui-col-md12">
				<div class="layui-card">
					<div class="layui-card-header">基于 layer 的抽屉扩展</div>
					<div class="layui-card-body">
						<p style="margin-bottom: 10px;">兼容原版 drawer 所有参数,要使用 layer 扩展,设置 legacy 选项为 false 即可</p>
						<button id="layerDrawerLeft" class="pear-btn pear-btn-success">左</button>
						<button id="layerDrawerRight" class="pear-btn pear-btn-success">右</button>
						<button id="layerDrawerTop" class="pear-btn pear-btn-success">上</button>
						<button id="layerDrawerBottom" class="pear-btn pear-btn-success">下</button>
						<button id="layerDrawer" class="pear-btn pear-btn-success">更多参数示例</button>
						<button id="layerDrawerParent" class="pear-btn pear-btn-success">父窗口打开</button>
						<button id="layerDrawerIframe" class="pear-btn pear-btn-success">iframe远程页面</button>
						<button id="layerDrawerURL" class="pear-btn pear-btn-success">URL远程页面</button>
						<div
							id="layertargetEl"
							style="width: 600px;
								height: 300px;
								overflow: hidden;
								position: relative;
								border: 1px solid gainsboro;
								background-color: #EEE;
								margin-top: 10px;
								display: flex;
								justify-content: space-around;
								align-items: center">
								<button id="layertargetDemo" class="pear-btn pear-btn-success">指定容器内打开</button>
						</div>
						<blockquote class="layui-elem-quote layui-quote-nm">指定容器内打开，需使用 layer 捕获层模式，并设置目标容器 style="overflow: hidden;
						position: relative;"</blockquote>
						<blockquote class="layui-elem-quote layui-quote-nm">
						  使用 url 参数指定抽屉代码片段地址，会使用 ajax 请求将代码片段拼接到抽屉中，和原来的页面在同一个页面上, 传值更方便。注意片段和原页面不能出现相同的 ID。
						</blockquote>
					</div>
				</div>
			</div>
			<div class="layui-col-md12">
				<div class="layui-collapse">
					<div class="layui-colla-item">
						<h2 class="layui-colla-title">显示代码</h2>
						<div class="layui-colla-content">
							<pre class="layui-code" lay-encode="true">
											$("#layerDrawerLeft").click(function(){
											  drawer.open({
											    legacy: false,
											    offset: 'l',
											    area: "30%",
											    content: "left内容",
											  })
											})
											$("#layerDrawerRight").click(function () {
											  drawer.open({
											    legacy: false,
											    offset: 'r',
											    area: "30%",
											    content: "right内容",
											  })
											})
											$("#layerDrawerTop").click(function () {
											  drawer.open({
											    legacy: false,
										      offset: "t",
											    content: "top内容",
											  })
											})
											$("#layerDrawerBottom").click(function () {
											  drawer.open({
											    legacy: false,
											    offset: "b",
											    content: "bottom内容",
											  })
											})

											$("#layerDrawer").click(function () {
											  var index = drawer.open({
											    legacy: false,
											    title: ['标题', 'font-size:16px;color:#2d8cf0'],
											    maxmin: true,
											    offset: "r",
											    area: "30%",
											    content: "抽屉内容",
											    btn:"关闭",
											    yes:function(index,layero){
											      drawer.close(index);
											      console.log(index,layero);
											    },
											    btnAlign: "l",
											    closeBtn: 1,
											  })
											})

											$("#layerDrawerParent").click(function () {

											  var index = parent.layui.drawer.open({
												    legacy: false,
												    title: ['标题', 'font-size:16px;color:#2d8cf0'],
												    maxmin: true,
												    offset: "r",
												    area: "30%",
												    content: "抽屉内容",
												    btn:"关闭",
												    yes:function(index,layero){
												      drawer.close(index);
												      console.log(index,layero);
												    },
												    btnAlign: "l",
												    closeBtn: 1,
												})
											})

											$("#layertargetDemo").click(function () {
											    drawer.open({
											    legacy: false,
											    target: "#layertargetEl",
											    direction: "right",
											    dom: ".layer-right",
											    distance: "50%",
											    });
											})

											$("#layerDrawerIframe").click(function () {
											  drawer.open({
											  legacy: false,
											  title: ['用户管理', 'font-size:16px;color:#2d8cf0'],
											  offset: 'r',
											  area: "80%",
											   maxmin: true,
											   closeBtn: 1,
											   iframe: window.location.origin + "/view/document/table.html"
											  })
											})

											$("#layerDrawerURL").click(function () {
											   drawer.open({
										     legacy: false,
										     title: ['用户管理', 'font-size:16px;color:#2d8cf0'],
											   offset: 'r',
											   area: "80%",
											   maxmin: true,
											   closeBtn: 1,
											   url: window.location.origin + "/view/document/drawerFragment.html"
											   })
											})
							</pre>
						</div>
					</div>
				</div>
			</div>
		</div>

			<div class="layer-left" style="display: none;">
				<br />
				<h3>左侧弹层内容...</h3>
			</div>
			<div class="layer-right" style="display: none;">
				<br />
				<h3>右侧弹层内容...</h3>
			</div>
			<div class="layer-top" style="display: none;">
				<br />
				<h3>上侧弹层内容...</h3>
			</div>
			<div class="layer-bottom" style="display: none;">
				<br />
				<h3>下侧弹层内容...</h3>
			</div>

			<div class="layer-open-mask" style="display: none;">
				<br />
				<h3>点击右侧灰色区域</h3>
			</div>
			<div class="layer-close-mask" style="display: none;">
				<button id="btnClose" class="pear-btn pear-btn-primary"
					style="position: absolute; bottom: 10px;left: 10px;">关闭抽屉</button>
			</div>
			<div class="layer-auto-close" style="display: none;">
				<br />
				<h3>打开 2秒 后自动关闭...</h3>
			</div>

	</body>

	<script src="../../component/layui/layui.js"></script>
	<script src="../../component/pear/pear.js"></script>
	<script>
		layui.use(['element', 'jquery', 'drawer', 'layer', 'code'], function() {
			var element = layui.element;
			var $ = layui.jquery;
			var drawer = layui.drawer;
			var layer = layui.layer;

			layui.code();

			$("#layerDrawerIframe").click(function () {
				drawer.open({
					legacy: false,
					title: ['用户管理', 'font-size:16px;color:#2d8cf0'],
					offset: 'r',
					area: "80%",
					maxmin: true,
					closeBtn: 1,
					iframe: window.location.origin + "/view/document/table.html"
				})
			})

			$("#layerDrawerURL").click(function () {
				drawer.open({
					legacy: false,
					title: ['用户管理', 'font-size:16px;color:#2d8cf0'],
					offset: 'r',
					area: "80%",
					maxmin: true,
					closeBtn: 1,
					url: window.location.origin + "/view/document/drawerFragment.html"
				})
			})

			$("#layerDrawerLeft").click(function(){
				drawer.open({
					legacy: false,
					offset: 'l',
					area: "30%",
					content: "left内容",
				})
			})
			$("#layerDrawerRight").click(function () {
				drawer.open({
					legacy: false,
					offset: 'r',
					area: "30%",
					content: "right内容",
				})
			})
			$("#layerDrawerTop").click(function () {
				drawer.open({
					legacy: false,
					offset: "t",
					content: "top内容",
				})
			})
			$("#layerDrawerBottom").click(function () {
				drawer.open({
					legacy: false,
					offset: "b",
					content: "bottom内容",
				})
			})

			$("#layerDrawer").click(function () {
				var index = drawer.open({
					legacy: false,
					title: ['标题', 'font-size:16px;color:#2d8cf0'],
					maxmin: true,
					offset: "r",
					area: "30%",
					content: "抽屉内容",
					btn:"关闭",
					yes:function(index,layero){
						drawer.close(index);
						console.log(index,layero);
					},
					btnAlign: "l",
					closeBtn: 1,
				})
			})

			$("#layerDrawerParent").click(function () {
				var index = parent.layui.drawer.open({
					legacy: false,
					title: ['标题', 'font-size:16px;color:#2d8cf0'],
					maxmin: true,
					offset: "r",
					area: "30%",
					content: "抽屉内容",
					btn: "关闭",
					yes: function (index, layero) {
						parent.layui.drawer.close(index);
						console.log(index, layero);
					},
					btnAlign: "l",
					closeBtn: 1,
				})
			})

			$("#layertargetDemo").click(function () {
				drawer.open({
					legacy: false,
					target: "#layertargetEl",
					direction: "right",
					dom: ".layer-right",
					distance: "50%",
				});
			})

			$(".drawer-right").click(function() {
				drawer.open({
					direction: "right",
					dom: ".layer-right",
					distance: "30%"
				});
			})

			$(".drawer-left").click(function() {

				drawer.open({
					direction: "left",
					dom: ".layer-left",
					distance: "30%"
				});

			})

			$(".drawer-top").click(function() {
				drawer.open({
					direction: "top",
					dom: ".layer-top",
					distance: "30%"
				});
			})

			$(".drawer-bottom").click(function() {
				drawer.open({
					direction: "bottom",
					dom: ".layer-bottom",
					distance: "30%"
				});
			})

			$(".drawer-open-mask").click(function() {
				drawer.open({
					direction: "right",
					dom: ".layer-open-mask",
					distance: "30%",
					maskClose: true
				});
			})

			var dom;

			$(".drawer-close-mask").click(function() {
				dom = drawer.open({
					direction: "right",
					dom: ".layer-close-mask",
					distance: "30%",
					maskClose: false
				});
			})

			$("#btnClose").click(function() {
				dom.close();
			})

			$(".drawer-auto-close").click(function() {
				dom = drawer.open({
					direction: "right",
					dom: ".layer-auto-close",
					distance: "30%",
					maskClose: false,
					time: 1000
				});
			})

			$(".drawer-call-back").click(function() {
				dom = drawer.open({
					direction: "right",
					dom: ".layer-right",
					distance: "30%",
					success: function() {
						layer.msg("触发回调函数", {
							icon: 1,
							time: 1000
						});
					}
				});
			})

			$("#targetDemo").click(function () {
				drawer.open({
					target: "#targetEl",
					direction: "right",
					dom: ".layer-right",
					distance: "50%",
				});
			})

		})
	</script>
</html>
