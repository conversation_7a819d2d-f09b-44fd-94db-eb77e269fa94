<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="utf-8">
		<title>404 - 页面未找到 | 四方支付系统</title>
		<link href="../../component/pear/css/pear.css" rel="stylesheet" />
		<style>
			* {
				margin: 0;
				padding: 0;
				box-sizing: border-box;
			}

			body {
				background: #0a0a0a;
				color: #ffffff;
				font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
				height: 100vh;
				display: flex;
				align-items: center;
				justify-content: center;
				overflow: hidden;
				position: relative;
			}

			/* 赛博朋克背景 */
			.cyber-bg {
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background: radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
							radial-gradient(circle at 80% 20%, rgba(255, 107, 107, 0.1) 0%, transparent 50%);
				z-index: -1;
			}

			.grid-lines {
				position: absolute;
				width: 100%;
				height: 100%;
				background-image:
					linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px),
					linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px);
				background-size: 50px 50px;
				animation: gridMove 20s linear infinite;
			}

			@keyframes gridMove {
				0% { transform: translate(0, 0); }
				100% { transform: translate(50px, 50px); }
			}

			/* 错误容器 */
			.error-container {
				text-align: center;
				z-index: 10;
				animation: fadeInUp 1s ease-out;
			}

			.error-code {
				font-size: 150px;
				font-weight: 900;
				color: #00d4ff;
				text-shadow: 0 0 50px rgba(0, 212, 255, 0.8);
				margin-bottom: 20px;
				animation: glitch 2s infinite;
				position: relative;
			}

			.error-code::before,
			.error-code::after {
				content: '404';
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
			}

			.error-code::before {
				color: #ff6b6b;
				animation: glitch-1 2s infinite;
				clip-path: polygon(0 0, 100% 0, 100% 45%, 0 45%);
			}

			.error-code::after {
				color: #4ecdc4;
				animation: glitch-2 2s infinite;
				clip-path: polygon(0 55%, 100% 55%, 100% 100%, 0 100%);
			}

			@keyframes glitch {
				0%, 100% { transform: translate(0); }
				20% { transform: translate(-2px, 2px); }
				40% { transform: translate(-2px, -2px); }
				60% { transform: translate(2px, 2px); }
				80% { transform: translate(2px, -2px); }
			}

			@keyframes glitch-1 {
				0%, 100% { transform: translate(0); }
				20% { transform: translate(2px, -2px); }
				40% { transform: translate(-2px, 2px); }
				60% { transform: translate(-2px, -2px); }
				80% { transform: translate(2px, 2px); }
			}

			@keyframes glitch-2 {
				0%, 100% { transform: translate(0); }
				20% { transform: translate(-2px, 2px); }
				40% { transform: translate(2px, -2px); }
				60% { transform: translate(2px, 2px); }
				80% { transform: translate(-2px, -2px); }
			}

			.error-title {
				font-size: 24px;
				font-weight: 600;
				color: #ffffff;
				margin-bottom: 15px;
				text-transform: uppercase;
				letter-spacing: 2px;
			}

			.error-message {
				font-size: 16px;
				color: #b0b0b0;
				margin-bottom: 40px;
				line-height: 1.6;
				max-width: 500px;
				margin-left: auto;
				margin-right: auto;
			}

			.cyber-btn {
				background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
				border: none;
				border-radius: 10px;
				color: #0a0a0a;
				font-size: 16px;
				font-weight: 700;
				padding: 15px 40px;
				cursor: pointer;
				transition: all 0.3s ease;
				text-decoration: none;
				display: inline-block;
				text-transform: uppercase;
				letter-spacing: 1px;
				position: relative;
				overflow: hidden;
			}

			.cyber-btn::before {
				content: '';
				position: absolute;
				top: 0;
				left: -100%;
				width: 100%;
				height: 100%;
				background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
				transition: left 0.5s ease;
			}

			.cyber-btn:hover {
				transform: translateY(-3px);
				box-shadow: 0 15px 40px rgba(0, 212, 255, 0.4);
			}

			.cyber-btn:hover::before {
				left: 100%;
			}

			@keyframes fadeInUp {
				from {
					opacity: 0;
					transform: translateY(50px);
				}
				to {
					opacity: 1;
					transform: translateY(0);
				}
			}

			/* 响应式 */
			@media (max-width: 768px) {
				.error-code {
					font-size: 100px;
				}

				.error-title {
					font-size: 20px;
				}

				.error-message {
					font-size: 14px;
					padding: 0 20px;
				}
			}
		</style>
	</head>
	<body>
		<div class="cyber-bg">
			<div class="grid-lines"></div>
		</div>

		<div class="error-container">
			<div class="error-code">404</div>
			<h1 class="error-title">Page Not Found</h1>
			<p class="error-message">
				抱歉，您访问的页面不存在或已被移除<br>
				请检查网址是否正确，或返回首页继续浏览
			</p>
			<a href="../../index.html" class="cyber-btn">返回首页</a>
		</div>

		<script src="../../component/layui/layui.js"></script>
		<script src="../../component/pear/pear.js"></script>
	</body>
</html>